"""Playwright configuration for end-to-end testing."""

import os
from playwright.sync_api import Play<PERSON>
from playwright.async_api import async_playwright

# Test configuration
BASE_URL = os.getenv("BASE_URL", "http://localhost:8000")
HEADLESS = os.getenv("HEADLESS", "true").lower() == "true"
SLOW_MO = int(os.getenv("SLOW_MO", "0"))
TIMEOUT = int(os.getenv("TIMEOUT", "30000"))

# Browser configuration
BROWSERS = ["chromium", "firefox", "webkit"]
VIEWPORT = {"width": 1280, "height": 720}

# Security testing configuration
SECURITY_HEADERS = [
    "Content-Security-Policy",
    "X-Frame-Options",
    "X-Content-Type-Options",
    "Strict-Transport-Security",
    "X-XSS-Protection",
    "Referrer-Policy",
]

# Authentication configuration
TEST_USERS = {
    "admin": {
        "username": "<EMAIL>",
        "password": "SecureTestPassword123!",
        "role": "admin",
    },
    "pentester": {
        "username": "<EMAIL>",
        "password": "SecureTestPassword123!",
        "role": "pentester",
    },
    "manager": {
        "username": "<EMAIL>",
        "password": "SecureTestPassword123!",
        "role": "project_manager",
    },
}

# Test data configuration
TEST_DATA = {
    "projects": {
        "test_project": {
            "name": "E2E Test Project",
            "description": "Project for end-to-end testing",
            "client_name": "Test Client",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
        }
    },
    "vulnerabilities": {
        "test_vuln": {
            "title": "Test SQL Injection",
            "description": "SQL injection vulnerability for testing",
            "severity": "HIGH",
            "cvss_score": 8.5,
        }
    },
}

# Performance thresholds
PERFORMANCE_THRESHOLDS = {
    "page_load": 3000,  # 3 seconds
    "api_response": 1000,  # 1 second
    "search": 2000,  # 2 seconds
    "form_submission": 5000,  # 5 seconds
}

# Security test configuration
SECURITY_TESTS = {
    "xss_payloads": [
        "<script>alert('XSS')</script>",
        "javascript:alert('XSS')",
        "<img src=x onerror=alert('XSS')>",
        "';alert('XSS');//",
    ],
    "sql_injection_payloads": [
        "' OR '1'='1",
        "'; DROP TABLE users; --",
        "' UNION SELECT * FROM users --",
        "admin'--",
    ],
    "csrf_test_endpoints": [
        "/api/v1/projects",
        "/api/v1/pentesters",
        "/api/v1/vulnerabilities",
    ],
}

# Accessibility configuration
ACCESSIBILITY_STANDARDS = {
    "wcag_level": "AA",
    "color_contrast_ratio": 4.5,
    "keyboard_navigation": True,
    "screen_reader_support": True,
}


class PlaywrightConfig:
    """Playwright configuration class."""
    
    def __init__(self):
        self.base_url = BASE_URL
        self.headless = HEADLESS
        self.slow_mo = SLOW_MO
        self.timeout = TIMEOUT
        self.browsers = BROWSERS
        self.viewport = VIEWPORT
        self.security_headers = SECURITY_HEADERS
        self.test_users = TEST_USERS
        self.test_data = TEST_DATA
        self.performance_thresholds = PERFORMANCE_THRESHOLDS
        self.security_tests = SECURITY_TESTS
        self.accessibility_standards = ACCESSIBILITY_STANDARDS
    
    def get_browser_config(self, browser_name: str) -> dict:
        """Get browser-specific configuration."""
        base_config = {
            "headless": self.headless,
            "slow_mo": self.slow_mo,
            "viewport": self.viewport,
            "ignore_https_errors": True,  # For testing environments
        }
        
        if browser_name == "chromium":
            base_config.update({
                "args": [
                    "--disable-web-security",
                    "--disable-features=VizDisplayCompositor",
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                ]
            })
        elif browser_name == "firefox":
            base_config.update({
                "firefox_user_prefs": {
                    "security.tls.insecure_fallback_hosts": "localhost",
                    "network.stricttransportsecurity.preloadlist": False,
                }
            })
        
        return base_config
    
    def get_context_config(self, user_type: str = None) -> dict:
        """Get browser context configuration."""
        config = {
            "viewport": self.viewport,
            "ignore_https_errors": True,
            "record_video_dir": "test-results/videos/",
            "record_har_path": "test-results/har/",
        }
        
        if user_type and user_type in self.test_users:
            # Pre-configure authentication state if needed
            config["storage_state"] = f"test-results/auth/{user_type}.json"
        
        return config
    
    def get_page_config(self) -> dict:
        """Get page configuration."""
        return {
            "default_timeout": self.timeout,
            "default_navigation_timeout": self.timeout * 2,
        }


# Global configuration instance
config = PlaywrightConfig()


def pytest_configure(config_obj):
    """Configure pytest for Playwright testing."""
    config_obj.addinivalue_line(
        "markers", "e2e: mark test as end-to-end test"
    )
    config_obj.addinivalue_line(
        "markers", "security: mark test as security test"
    )
    config_obj.addinivalue_line(
        "markers", "performance: mark test as performance test"
    )
    config_obj.addinivalue_line(
        "markers", "accessibility: mark test as accessibility test"
    )
    config_obj.addinivalue_line(
        "markers", "smoke: mark test as smoke test"
    )


def pytest_collection_modifyitems(config_obj, items):
    """Modify test collection for Playwright tests."""
    for item in items:
        # Add e2e marker to all tests in e2e directory
        if "e2e" in str(item.fspath):
            item.add_marker("e2e")
        
        # Add security marker to security tests
        if "security" in str(item.fspath) or "security" in item.name:
            item.add_marker("security")
        
        # Add performance marker to performance tests
        if "performance" in str(item.fspath) or "performance" in item.name:
            item.add_marker("performance")


# Export configuration for use in tests
__all__ = ["config", "PlaywrightConfig"]
