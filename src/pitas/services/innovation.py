"""Innovation management and technology evaluation service."""

import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from uuid import UUID

from sqlalchemy import func, desc, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from pitas.db.models.innovation import (
    InnovationProject, InnovationEvaluation, InnovationMilestone,
    TechnologyTrend, ProofOfConcept,
    InnovationStatus, TechnologyCategory, PriorityLevel
)
from pitas.schemas.innovation import (
    InnovationProjectCreate, InnovationProjectUpdate,
    InnovationEvaluationCreate,
    InnovationMilestoneCreate, InnovationMilestoneUpdate,
    TechnologyTrendCreate, TechnologyTrendUpdate,
    ProofOfConceptCreate, ProofOfConceptUpdate,
    InnovationPipeline, InnovationMetrics
)
from pitas.services.base import BaseService

logger = logging.getLogger(__name__)


class InnovationService(BaseService[InnovationProject, InnovationProjectCreate, InnovationProjectUpdate]):
    """Service for managing innovation projects and technology evaluation."""

    def __init__(self, db: AsyncSession):
        super().__init__(InnovationProject, db)

    async def create_innovation_project(
        self,
        proposed_by: UUID,
        project_data: InnovationProjectCreate
    ) -> InnovationProject:
        """Create new innovation project."""
        try:
            project = InnovationProject(
                proposed_by=proposed_by,
                **project_data.model_dump()
            )
            
            # Calculate initial overall score
            project.overall_score = await self._calculate_initial_score(project)
            
            self.db.add(project)
            await self.db.commit()
            await self.db.refresh(project)
            
            logger.info(f"Created innovation project {project.id}")
            return project
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error creating innovation project: {str(e)}")
            raise

    async def evaluate_project(
        self,
        project_id: UUID,
        evaluator_id: UUID,
        evaluation_data: InnovationEvaluationCreate
    ) -> InnovationEvaluation:
        """Add evaluation to innovation project."""
        try:
            evaluation = InnovationEvaluation(
                project_id=project_id,
                evaluator_id=evaluator_id,
                **evaluation_data.model_dump()
            )
            
            self.db.add(evaluation)
            
            # Update project scores based on evaluation
            await self._update_project_scores(project_id)
            
            await self.db.commit()
            await self.db.refresh(evaluation)
            
            logger.info(f"Added evaluation to project {project_id}")
            return evaluation
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error adding project evaluation: {str(e)}")
            raise

    async def update_project_status(
        self,
        project_id: UUID,
        status: InnovationStatus,
        assigned_to: Optional[UUID] = None
    ) -> InnovationProject:
        """Update innovation project status."""
        try:
            project = await self.get(project_id)
            if not project:
                raise ValueError(f"Project {project_id} not found")
            
            old_status = project.status
            project.status = status
            
            if assigned_to:
                project.assigned_to = assigned_to
            
            # Update timestamps based on status changes
            if old_status == InnovationStatus.IDEA and status != InnovationStatus.IDEA:
                project.started_at = datetime.utcnow()
            elif status in [InnovationStatus.DEPLOYED, InnovationStatus.REJECTED]:
                project.completed_at = datetime.utcnow()
                project.progress_percentage = 100.0
            
            await self.db.commit()
            await self.db.refresh(project)
            
            logger.info(f"Updated project {project_id} status to {status}")
            return project
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error updating project status: {str(e)}")
            raise

    async def create_milestone(
        self,
        milestone_data: InnovationMilestoneCreate
    ) -> InnovationMilestone:
        """Create project milestone."""
        try:
            milestone = InnovationMilestone(**milestone_data.model_dump())
            
            self.db.add(milestone)
            await self.db.commit()
            await self.db.refresh(milestone)
            
            logger.info(f"Created milestone for project {milestone_data.project_id}")
            return milestone
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error creating milestone: {str(e)}")
            raise

    async def update_milestone(
        self,
        milestone_id: UUID,
        milestone_data: InnovationMilestoneUpdate
    ) -> InnovationMilestone:
        """Update project milestone."""
        try:
            milestone = await self.db.get(InnovationMilestone, milestone_id)
            if not milestone:
                raise ValueError(f"Milestone {milestone_id} not found")
            
            for field, value in milestone_data.model_dump(exclude_unset=True).items():
                setattr(milestone, field, value)
            
            # Update project progress if milestone completed
            if milestone_data.is_completed and not milestone.is_completed:
                await self._update_project_progress(milestone.project_id)
            
            await self.db.commit()
            await self.db.refresh(milestone)
            
            logger.info(f"Updated milestone {milestone_id}")
            return milestone
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error updating milestone: {str(e)}")
            raise

    async def track_technology_trend(
        self,
        trend_data: TechnologyTrendCreate
    ) -> TechnologyTrend:
        """Track new technology trend."""
        try:
            trend = TechnologyTrend(**trend_data.model_dump())
            
            self.db.add(trend)
            await self.db.commit()
            await self.db.refresh(trend)
            
            logger.info(f"Tracked new technology trend: {trend.name}")
            return trend
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error tracking technology trend: {str(e)}")
            raise

    async def create_proof_of_concept(
        self,
        lead_researcher: UUID,
        poc_data: ProofOfConceptCreate
    ) -> ProofOfConcept:
        """Create proof of concept project."""
        try:
            poc = ProofOfConcept(
                lead_researcher=lead_researcher,
                **poc_data.model_dump()
            )
            
            # Calculate duration if end_date provided
            if poc_data.end_date:
                duration = (poc_data.end_date - poc_data.start_date).days
                poc.duration_days = duration
            
            self.db.add(poc)
            await self.db.commit()
            await self.db.refresh(poc)
            
            logger.info(f"Created proof of concept: {poc.title}")
            return poc
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error creating proof of concept: {str(e)}")
            raise

    async def get_innovation_pipeline(self) -> InnovationPipeline:
        """Get innovation pipeline overview."""
        try:
            # Total projects
            total_projects = await self.db.scalar(func.count(InnovationProject.id))
            
            # Projects by status
            projects_by_status = {}
            for status in InnovationStatus:
                count = await self.db.scalar(
                    func.count(InnovationProject.id).filter(
                        InnovationProject.status == status
                    )
                )
                projects_by_status[status.value] = count or 0
            
            # Projects by category
            projects_by_category = {}
            for category in TechnologyCategory:
                count = await self.db.scalar(
                    func.count(InnovationProject.id).filter(
                        InnovationProject.category == category
                    )
                )
                projects_by_category[category.value] = count or 0
            
            # Projects by priority
            projects_by_priority = {}
            for priority in PriorityLevel:
                count = await self.db.scalar(
                    func.count(InnovationProject.id).filter(
                        InnovationProject.priority == priority
                    )
                )
                projects_by_priority[priority.value] = count or 0
            
            # Average completion time for completed projects
            avg_completion_time = await self.db.scalar(
                func.avg(
                    func.extract('epoch', InnovationProject.completed_at - InnovationProject.started_at) / 86400
                ).filter(
                    and_(
                        InnovationProject.completed_at.isnot(None),
                        InnovationProject.started_at.isnot(None)
                    )
                )
            )
            
            # Success rate (deployed projects / completed projects)
            deployed_count = await self.db.scalar(
                func.count(InnovationProject.id).filter(
                    InnovationProject.status == InnovationStatus.DEPLOYED
                )
            )
            completed_count = await self.db.scalar(
                func.count(InnovationProject.id).filter(
                    InnovationProject.status.in_([
                        InnovationStatus.DEPLOYED,
                        InnovationStatus.REJECTED
                    ])
                )
            )
            success_rate = (deployed_count / completed_count * 100) if completed_count > 0 else None
            
            # Total investment and projected ROI
            total_investment = await self.db.scalar(
                func.sum(InnovationProject.estimated_cost).filter(
                    InnovationProject.estimated_cost.isnot(None)
                )
            )
            
            projected_roi = await self.db.scalar(
                func.sum(InnovationProject.expected_roi).filter(
                    InnovationProject.expected_roi.isnot(None)
                )
            )
            
            return InnovationPipeline(
                total_projects=total_projects or 0,
                projects_by_status=projects_by_status,
                projects_by_category=projects_by_category,
                projects_by_priority=projects_by_priority,
                average_completion_time=float(avg_completion_time) if avg_completion_time else None,
                success_rate=float(success_rate) if success_rate else None,
                total_investment=float(total_investment) if total_investment else None,
                projected_roi=float(projected_roi) if projected_roi else None
            )
            
        except Exception as e:
            logger.error(f"Error getting innovation pipeline: {str(e)}")
            raise

    async def get_trending_technologies(
        self,
        limit: int = 10,
        category: Optional[TechnologyCategory] = None
    ) -> List[TechnologyTrend]:
        """Get trending technologies."""
        try:
            query = self.db.query(TechnologyTrend).order_by(
                desc(TechnologyTrend.relevance_score),
                desc(TechnologyTrend.last_updated)
            )
            
            if category:
                query = query.filter(TechnologyTrend.category == category)
            
            result = await self.db.execute(query.limit(limit))
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Error getting trending technologies: {str(e)}")
            raise

    async def _calculate_initial_score(self, project: InnovationProject) -> float:
        """Calculate initial project score based on basic criteria."""
        score = 5.0  # Base score
        
        # Adjust based on priority
        if project.priority == PriorityLevel.CRITICAL:
            score += 2.0
        elif project.priority == PriorityLevel.HIGH:
            score += 1.0
        elif project.priority == PriorityLevel.LOW:
            score -= 1.0
        
        # Adjust based on expected ROI
        if project.expected_roi:
            if project.expected_roi > 5.0:
                score += 1.5
            elif project.expected_roi > 2.0:
                score += 1.0
            elif project.expected_roi < 1.0:
                score -= 1.0
        
        return min(max(score, 1.0), 10.0)

    async def _update_project_scores(self, project_id: UUID) -> None:
        """Update project scores based on evaluations."""
        try:
            # Get all evaluations for the project
            result = await self.db.execute(
                self.db.query(InnovationEvaluation).filter(
                    InnovationEvaluation.project_id == project_id
                )
            )
            evaluations = result.scalars().all()
            
            if not evaluations:
                return
            
            # Calculate average scores
            feasibility_avg = sum(e.technical_feasibility for e in evaluations) / len(evaluations)
            impact_avg = sum(e.business_impact for e in evaluations) / len(evaluations)
            effort_avg = sum(e.implementation_effort for e in evaluations) / len(evaluations)
            risk_avg = sum(e.risk_assessment for e in evaluations) / len(evaluations)
            
            # Calculate overall score (weighted average)
            overall_score = (
                feasibility_avg * 0.25 +
                impact_avg * 0.35 +
                (10 - effort_avg) * 0.25 +  # Lower effort is better
                (10 - risk_avg) * 0.15      # Lower risk is better
            )
            
            # Update project
            project = await self.get(project_id)
            if project:
                project.feasibility_score = feasibility_avg
                project.impact_score = impact_avg
                project.effort_score = effort_avg
                project.risk_score = risk_avg
                project.overall_score = overall_score
                
                await self.db.commit()
            
        except Exception as e:
            logger.error(f"Error updating project scores: {str(e)}")
            raise

    async def _update_project_progress(self, project_id: UUID) -> None:
        """Update project progress based on completed milestones."""
        try:
            # Get milestone completion stats
            total_milestones = await self.db.scalar(
                func.count(InnovationMilestone.id).filter(
                    InnovationMilestone.project_id == project_id
                )
            )
            
            completed_milestones = await self.db.scalar(
                func.count(InnovationMilestone.id).filter(
                    and_(
                        InnovationMilestone.project_id == project_id,
                        InnovationMilestone.is_completed == True
                    )
                )
            )
            
            if total_milestones > 0:
                progress = (completed_milestones / total_milestones) * 100
                
                # Update project
                project = await self.get(project_id)
                if project:
                    project.progress_percentage = progress
                    project.milestones_completed = completed_milestones
                    project.total_milestones = total_milestones
                    
                    await self.db.commit()
            
        except Exception as e:
            logger.error(f"Error updating project progress: {str(e)}")
            raise
