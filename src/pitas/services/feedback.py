"""Feedback collection and analysis service."""

import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from uuid import UUID

from sqlalchemy import func, desc, and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from pitas.db.models.feedback import (
    UserFeedback, FeedbackResponse, UserBehaviorAnalytics,
    FeatureUsageMetrics, SatisfactionSurvey,
    FeedbackType, FeedbackStatus, SentimentScore
)
from pitas.schemas.feedback import (
    UserFeedbackCreate, UserFeedbackUpdate,
    FeedbackResponseCreate,
    UserBehaviorAnalyticsCreate,
    FeatureUsageMetricsCreate, FeatureUsageMetricsUpdate,
    SatisfactionSurveyCreate,
    FeedbackAnalytics, UserEngagementMetrics, FeedbackTrends
)
from pitas.services.base import BaseService

logger = logging.getLogger(__name__)


class FeedbackService(BaseService[UserFeedback, UserFeedbackCreate, UserFeedbackUpdate]):
    """Service for managing user feedback and analytics."""

    def __init__(self, db: AsyncSession):
        super().__init__(UserFeedback, db)

    async def create_feedback(
        self,
        user_id: UUID,
        feedback_data: UserFeedbackCreate
    ) -> UserFeedback:
        """Create new user feedback with automatic analysis."""
        try:
            # Create feedback record
            feedback = UserFeedback(
                user_id=user_id,
                **feedback_data.model_dump()
            )
            
            # Perform automatic sentiment analysis (simplified)
            feedback.sentiment_score = await self._analyze_sentiment(feedback_data.description)
            feedback.priority_score = await self._calculate_priority_score(feedback)
            feedback.category_tags = await self._extract_category_tags(feedback_data.description)
            
            self.db.add(feedback)
            await self.db.commit()
            await self.db.refresh(feedback)
            
            logger.info(f"Created feedback {feedback.id} for user {user_id}")
            return feedback
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error creating feedback: {str(e)}")
            raise

    async def respond_to_feedback(
        self,
        feedback_id: UUID,
        responder_id: UUID,
        response_data: FeedbackResponseCreate
    ) -> FeedbackResponse:
        """Add response to user feedback."""
        try:
            response = FeedbackResponse(
                feedback_id=feedback_id,
                responder_id=responder_id,
                response_text=response_data.response_text,
                is_public=response_data.is_public
            )
            
            self.db.add(response)
            await self.db.commit()
            await self.db.refresh(response)
            
            logger.info(f"Added response to feedback {feedback_id}")
            return response
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error adding feedback response: {str(e)}")
            raise

    async def update_feedback_status(
        self,
        feedback_id: UUID,
        status: FeedbackStatus,
        assigned_to: Optional[UUID] = None,
        resolution_notes: Optional[str] = None
    ) -> UserFeedback:
        """Update feedback status and assignment."""
        try:
            feedback = await self.get(feedback_id)
            if not feedback:
                raise ValueError(f"Feedback {feedback_id} not found")
            
            feedback.status = status
            if assigned_to:
                feedback.assigned_to = assigned_to
            if resolution_notes:
                feedback.resolution_notes = resolution_notes
            
            if status in [FeedbackStatus.IMPLEMENTED, FeedbackStatus.REJECTED]:
                feedback.resolved_at = datetime.utcnow()
            
            await self.db.commit()
            await self.db.refresh(feedback)
            
            logger.info(f"Updated feedback {feedback_id} status to {status}")
            return feedback
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error updating feedback status: {str(e)}")
            raise

    async def track_user_behavior(
        self,
        user_id: UUID,
        analytics_data: UserBehaviorAnalyticsCreate
    ) -> UserBehaviorAnalytics:
        """Track user behavior for analytics."""
        try:
            analytics = UserBehaviorAnalytics(
                user_id=user_id,
                **analytics_data.model_dump()
            )
            
            self.db.add(analytics)
            await self.db.commit()
            await self.db.refresh(analytics)
            
            return analytics
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error tracking user behavior: {str(e)}")
            raise

    async def update_feature_usage(
        self,
        user_id: UUID,
        feature_name: str,
        usage_data: FeatureUsageMetricsCreate
    ) -> FeatureUsageMetrics:
        """Update or create feature usage metrics."""
        try:
            # Check if metrics already exist
            existing = await self.db.execute(
                self.db.query(FeatureUsageMetrics).filter(
                    and_(
                        FeatureUsageMetrics.user_id == user_id,
                        FeatureUsageMetrics.feature_name == feature_name
                    )
                )
            )
            metrics = existing.scalar_one_or_none()
            
            if metrics:
                # Update existing metrics
                metrics.usage_count += usage_data.usage_count
                metrics.total_time_spent += usage_data.total_time_spent
                metrics.error_count += usage_data.error_count
                if usage_data.success_rate is not None:
                    metrics.success_rate = usage_data.success_rate
                if usage_data.usage_context:
                    metrics.usage_context = usage_data.usage_context
                metrics.last_used = datetime.utcnow()
            else:
                # Create new metrics
                metrics = FeatureUsageMetrics(
                    user_id=user_id,
                    feature_name=feature_name,
                    **usage_data.model_dump()
                )
                self.db.add(metrics)
            
            await self.db.commit()
            await self.db.refresh(metrics)
            
            return metrics
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error updating feature usage: {str(e)}")
            raise

    async def create_satisfaction_survey(
        self,
        user_id: UUID,
        survey_data: SatisfactionSurveyCreate
    ) -> SatisfactionSurvey:
        """Create satisfaction survey response."""
        try:
            survey = SatisfactionSurvey(
                user_id=user_id,
                **survey_data.model_dump()
            )
            
            self.db.add(survey)
            await self.db.commit()
            await self.db.refresh(survey)
            
            logger.info(f"Created satisfaction survey for user {user_id}")
            return survey
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error creating satisfaction survey: {str(e)}")
            raise

    async def get_feedback_analytics(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> FeedbackAnalytics:
        """Get comprehensive feedback analytics."""
        try:
            if not start_date:
                start_date = datetime.utcnow() - timedelta(days=30)
            if not end_date:
                end_date = datetime.utcnow()
            
            # Total feedback count
            total_feedback = await self.db.scalar(
                func.count(UserFeedback.id).filter(
                    UserFeedback.created_at.between(start_date, end_date)
                )
            )
            
            # Feedback by type
            feedback_by_type = {}
            for feedback_type in FeedbackType:
                count = await self.db.scalar(
                    func.count(UserFeedback.id).filter(
                        and_(
                            UserFeedback.feedback_type == feedback_type,
                            UserFeedback.created_at.between(start_date, end_date)
                        )
                    )
                )
                feedback_by_type[feedback_type.value] = count or 0
            
            # Feedback by status
            feedback_by_status = {}
            for status in FeedbackStatus:
                count = await self.db.scalar(
                    func.count(UserFeedback.id).filter(
                        and_(
                            UserFeedback.status == status,
                            UserFeedback.created_at.between(start_date, end_date)
                        )
                    )
                )
                feedback_by_status[status.value] = count or 0
            
            # Average ratings
            avg_rating = await self.db.scalar(
                func.avg(UserFeedback.rating).filter(
                    and_(
                        UserFeedback.rating.isnot(None),
                        UserFeedback.created_at.between(start_date, end_date)
                    )
                )
            )
            
            avg_nps = await self.db.scalar(
                func.avg(UserFeedback.nps_score).filter(
                    and_(
                        UserFeedback.nps_score.isnot(None),
                        UserFeedback.created_at.between(start_date, end_date)
                    )
                )
            )
            
            # Sentiment distribution
            sentiment_distribution = {}
            for sentiment in SentimentScore:
                count = await self.db.scalar(
                    func.count(UserFeedback.id).filter(
                        and_(
                            UserFeedback.sentiment_score == sentiment,
                            UserFeedback.created_at.between(start_date, end_date)
                        )
                    )
                )
                sentiment_distribution[sentiment.value] = count or 0
            
            # Resolution time
            resolution_time = await self.db.scalar(
                func.avg(
                    func.extract('epoch', UserFeedback.resolved_at - UserFeedback.created_at) / 3600
                ).filter(
                    and_(
                        UserFeedback.resolved_at.isnot(None),
                        UserFeedback.created_at.between(start_date, end_date)
                    )
                )
            )
            
            return FeedbackAnalytics(
                total_feedback=total_feedback or 0,
                feedback_by_type=feedback_by_type,
                feedback_by_status=feedback_by_status,
                average_rating=float(avg_rating) if avg_rating else None,
                average_nps_score=float(avg_nps) if avg_nps else None,
                sentiment_distribution=sentiment_distribution,
                resolution_time_avg_hours=float(resolution_time) if resolution_time else None,
                top_feature_areas=[],  # TODO: Implement
                trending_issues=[]  # TODO: Implement
            )
            
        except Exception as e:
            logger.error(f"Error getting feedback analytics: {str(e)}")
            raise

    async def _analyze_sentiment(self, text: str) -> SentimentScore:
        """Analyze sentiment of feedback text (simplified implementation)."""
        # This is a simplified implementation
        # In production, you would use a proper NLP service
        positive_words = ['good', 'great', 'excellent', 'love', 'amazing', 'perfect']
        negative_words = ['bad', 'terrible', 'hate', 'awful', 'horrible', 'worst']
        
        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        if positive_count > negative_count:
            return SentimentScore.POSITIVE if positive_count > 2 else SentimentScore.NEUTRAL
        elif negative_count > positive_count:
            return SentimentScore.NEGATIVE if negative_count > 2 else SentimentScore.NEUTRAL
        else:
            return SentimentScore.NEUTRAL

    async def _calculate_priority_score(self, feedback: UserFeedback) -> float:
        """Calculate priority score for feedback."""
        score = 5.0  # Base score
        
        # Adjust based on feedback type
        if feedback.feedback_type == FeedbackType.BUG_REPORT:
            score += 2.0
        elif feedback.feedback_type == FeedbackType.PERFORMANCE_ISSUE:
            score += 1.5
        elif feedback.feedback_type == FeedbackType.FEATURE_REQUEST:
            score += 1.0
        
        # Adjust based on rating
        if feedback.rating:
            if feedback.rating <= 2:
                score += 2.0
            elif feedback.rating <= 3:
                score += 1.0
        
        # Adjust based on NPS score
        if feedback.nps_score is not None:
            if feedback.nps_score <= 6:
                score += 1.5
            elif feedback.nps_score <= 8:
                score += 0.5
        
        return min(score, 10.0)

    async def _extract_category_tags(self, text: str) -> Dict[str, Any]:
        """Extract category tags from feedback text."""
        # Simplified implementation
        categories = {
            'ui': ['interface', 'ui', 'design', 'layout', 'button'],
            'performance': ['slow', 'fast', 'speed', 'performance', 'loading'],
            'security': ['security', 'login', 'password', 'access'],
            'workflow': ['workflow', 'process', 'step', 'procedure']
        }
        
        text_lower = text.lower()
        tags = {}
        
        for category, keywords in categories.items():
            matches = sum(1 for keyword in keywords if keyword in text_lower)
            if matches > 0:
                tags[category] = matches
        
        return tags
