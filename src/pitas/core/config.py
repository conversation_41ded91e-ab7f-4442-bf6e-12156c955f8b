"""Application configuration settings."""

from functools import lru_cache
from typing import Any, Dict, Optional

from pydantic import Field, PostgresDsn, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support.

    All settings can be overridden by environment variables with the
    same name but in uppercase.
    """

    # API Settings
    api_v1_str: str = "/api/v1"
    project_name: str = "PITAS - Pentesting Team Management System"
    project_version: str = "0.8.0"  # Phase 8 + Phase 11 integration complete
    description: str = "A comprehensive platform for managing global pentesting teams"

    # Security
    secret_key: str = Field(..., description="Secret key for JWT encoding")
    access_token_expire_minutes: int = 30
    algorithm: str = "HS256"

    # Database
    postgres_server: str = Field(..., description="PostgreSQL server host")
    postgres_user: str = Field(..., description="PostgreSQL username")
    postgres_password: str = Field(..., description="PostgreSQL password")
    postgres_db: str = Field(..., description="PostgreSQL database name")
    postgres_port: int = Field(default=5432, description="PostgreSQL port")

    # Redis
    redis_url: str = Field(default="redis://localhost:6379/0", description="Redis connection URL")

    # Neo4j (for vulnerability correlation)
    neo4j_uri: str = Field(default="bolt://localhost:7687", description="Neo4j connection URI")
    neo4j_user: str = Field(default="neo4j", description="Neo4j username")
    neo4j_password: str = Field(..., description="Neo4j password")

    # InfluxDB (for time-series analytics)
    influxdb_url: str = Field(default="http://localhost:8086", description="InfluxDB URL")
    influxdb_token: str = Field(..., description="InfluxDB access token")
    influxdb_org: str = Field(default="pitas", description="InfluxDB organization")
    influxdb_bucket: str = Field(default="security_metrics", description="InfluxDB bucket")

    # Celery
    celery_broker_url: str = Field(default="redis://localhost:6379/1", description="Celery broker URL")
    celery_result_backend: str = Field(default="redis://localhost:6379/2", description="Celery result backend")

    # Environment
    environment: str = Field(default="development", description="Environment name")
    debug: bool = Field(default=False, description="Debug mode")
    testing: bool = Field(default=False, description="Testing mode")

    # Logging
    log_level: str = Field(default="INFO", description="Logging level")

    # CORS
    backend_cors_origins: list[str] = Field(
        default_factory=list,
        description="List of allowed CORS origins"
    )

    # External APIs
    mitre_attack_api_url: str = Field(
        default="https://attack.mitre.org/api",
        description="MITRE ATT&CK API URL"
    )
    nvd_api_key: Optional[str] = Field(default=None, description="NVD API key")
    nist_api_url: str = Field(
        default="https://services.nvd.nist.gov/rest/json",
        description="NIST API URL"
    )

    # Monitoring
    prometheus_metrics_port: int = Field(default=9090, description="Prometheus metrics port")
    jaeger_agent_host: str = Field(default="localhost", description="Jaeger agent host")
    jaeger_agent_port: int = Field(default=6831, description="Jaeger agent port")

    # Service Discovery & Traefik
    traefik_network: str = Field(default="traefik_network", description="Traefik network name")
    pitas_network: str = Field(default="pitas_net", description="PITAS internal network name")
    service_domain: str = Field(default="pitas.localhost", description="Base domain for services")
    traefik_api_url: str = Field(default="http://localhost:8080", description="Traefik API URL")
    service_registry_enabled: bool = Field(default=True, description="Enable service registry")

    # Rate Limiting
    rate_limit_per_minute: int = Field(default=60, description="Rate limit per minute")
    rate_limit_burst: int = Field(default=10, description="Rate limit burst")

    # File Upload
    upload_path: str = Field(default="/tmp/pitas/uploads", description="Upload directory path")
    max_upload_size: int = Field(default=10485760, description="Maximum upload size in bytes")

    # Data Storage
    data_dir: str = Field(default="/tmp/pitas/data", description="Data directory for analytics and reports")


    # Phase 7: Integration Layer Settings
    # Obsidian Integration
    obsidian_vault_path: str = Field(
        default="/opt/obsidian/vault",
        description="Path to Obsidian vault directory"
    )
    obsidian_api_url: str = Field(
        default="http://localhost:27123",
        description="Obsidian Local REST API URL"
    )
    obsidian_api_key: str = Field(
        default="",
        description="Obsidian API key for authentication"
    )
    obsidian_sync_interval: int = Field(
        default=300,
        description="Obsidian sync interval in seconds"
    )

    # CMDB Integration
    cmdb_type: str = Field(
        default="servicenow",
        description="CMDB system type (servicenow, bmc_remedy, custom)"
    )
    cmdb_api_url: str = Field(
        default="https://your-instance.service-now.com/api",
        description="CMDB API base URL"
    )
    cmdb_username: str = Field(default="", description="CMDB username")
    cmdb_password: str = Field(default="", description="CMDB password")
    cmdb_sync_interval: int = Field(
        default=600,
        description="CMDB sync interval in seconds"
    )

    # Security Tools Integration
    siem_type: str = Field(
        default="splunk",
        description="SIEM system type (splunk, qradar, sentinel)"
    )
    siem_api_url: str = Field(
        default="https://your-splunk.com:8089",
        description="SIEM API URL"
    )
    siem_api_key: str = Field(default="", description="SIEM API key")

    vulnerability_scanner_type: str = Field(
        default="nessus",
        description="Vulnerability scanner type (nessus, qualys, rapid7)"
    )
    vulnerability_scanner_api_url: str = Field(
        default="https://your-nessus.com:8834",
        description="Vulnerability scanner API URL"
    )
    vulnerability_scanner_api_key: str = Field(
        default="",
        description="Vulnerability scanner API key"
    )

    # Integration Platform Settings
    integration_max_retries: int = Field(
        default=3,
        description="Maximum retries for integration API calls"
    )
    integration_timeout: int = Field(
        default=30,
        description="Integration API timeout in seconds"
    )
    integration_batch_size: int = Field(
        default=100,
        description="Batch size for bulk operations"
    )

    # Workflow Configuration
    ptes_phases: list[str] = Field(
        default=[
            "pre_engagement",
            "intelligence_gathering",
            "threat_modeling",
            "vulnerability_analysis",
            "exploitation",
            "post_exploitation",
            "reporting"
        ],
        description="PTES methodology phases"
    )

    # SLA Configuration (in hours)
    sla_critical: int = Field(default=4, description="SLA for critical vulnerabilities (hours)")
    sla_high: int = Field(default=24, description="SLA for high vulnerabilities (hours)")
    sla_medium: int = Field(default=72, description="SLA for medium vulnerabilities (hours)")
    sla_low: int = Field(default=168, description="SLA for low vulnerabilities (hours)")

    # Escalation Configuration
    escalation_levels: int = Field(default=3, description="Number of escalation levels")
    escalation_interval_hours: int = Field(default=2, description="Hours between escalation levels")

    # Ticketing Integration
    jira_enabled: bool = Field(default=False, description="Enable Jira integration")
    jira_url: Optional[str] = Field(default=None, description="Jira instance URL")
    jira_username: Optional[str] = Field(default=None, description="Jira username")
    jira_api_token: Optional[str] = Field(default=None, description="Jira API token")

    servicenow_enabled: bool = Field(default=False, description="Enable ServiceNow integration")
    servicenow_url: Optional[str] = Field(default=None, description="ServiceNow instance URL")
    servicenow_username: Optional[str] = Field(default=None, description="ServiceNow username")
    servicenow_password: Optional[str] = Field(default=None, description="ServiceNow password")

    zendesk_enabled: bool = Field(default=False, description="Enable Zendesk integration")
    zendesk_url: Optional[str] = Field(default=None, description="Zendesk instance URL")
    zendesk_email: Optional[str] = Field(default=None, description="Zendesk email")
    zendesk_token: Optional[str] = Field(default=None, description="Zendesk API token")


    @validator("backend_cors_origins", pre=True)
    def assemble_cors_origins(cls, v: str | list[str]) -> list[str]:
        """Parse CORS origins from string or list."""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    @property
    def database_url(self) -> PostgresDsn:
        """Construct database URL from individual components."""
        return PostgresDsn.build(
            scheme="postgresql+asyncpg",
            username=self.postgres_user,
            password=self.postgres_password,
            host=self.postgres_server,
            port=self.postgres_port,
            path=f"/{self.postgres_db}",
        )

    @property
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment.lower() == "production"

    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance.

    Returns:
        Settings: Application settings instance.
    """
    return Settings()


# Global settings instance
settings = get_settings()