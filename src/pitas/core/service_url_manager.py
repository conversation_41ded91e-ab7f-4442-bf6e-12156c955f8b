"""Service URL Manager for Traefik integration.

This module provides centralized management of service URLs and Traefik registration
for the PITAS application. It handles service discovery, URL generation, and
health monitoring for all services in the system.
"""

import asyncio
import json
from typing import Dict, List, Optional, Set
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import structlog
import httpx
from pydantic import BaseModel, Field

from pitas.core.config import settings


logger = structlog.get_logger(__name__)


@dataclass
class ServiceInfo:
    """Information about a registered service."""
    name: str
    url: str
    internal_port: int
    health_endpoint: str = "/health"
    tags: List[str] = None
    metadata: Dict[str, str] = None
    registered_at: datetime = None
    last_health_check: Optional[datetime] = None
    is_healthy: bool = True

    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.metadata is None:
            self.metadata = {}
        if self.registered_at is None:
            self.registered_at = datetime.utcnow()


class ServiceRegistration(BaseModel):
    """Pydantic model for service registration requests."""
    name: str = Field(..., description="Service name")
    internal_port: int = Field(..., description="Internal service port")
    health_endpoint: str = Field(default="/health", description="Health check endpoint")
    tags: List[str] = Field(default_factory=list, description="Service tags")
    metadata: Dict[str, str] = Field(default_factory=dict, description="Service metadata")


class ServiceURLManager:
    """Manages service URLs and Traefik integration for PITAS services.
    
    This class provides:
    - Service registration and deregistration
    - URL generation for services using *.pitas.localhost pattern
    - Health monitoring of registered services
    - Traefik configuration management
    """

    def __init__(self):
        """Initialize the Service URL Manager."""
        self._services: Dict[str, ServiceInfo] = {}
        self._health_check_interval = 30  # seconds
        self._health_check_task: Optional[asyncio.Task] = None
        self._client = httpx.AsyncClient(timeout=10.0)

    async def register_service(self, registration: ServiceRegistration) -> str:
        """Register a new service with the URL manager.
        
        Args:
            registration: Service registration information
            
        Returns:
            str: Generated service URL
            
        Raises:
            ValueError: If service name is already registered
        """
        if registration.name in self._services:
            raise ValueError(f"Service '{registration.name}' is already registered")

        # Generate service URL using *.pitas.localhost pattern
        service_url = self._generate_service_url(registration.name)
        
        # Create service info
        service_info = ServiceInfo(
            name=registration.name,
            url=service_url,
            internal_port=registration.internal_port,
            health_endpoint=registration.health_endpoint,
            tags=registration.tags,
            metadata=registration.metadata
        )
        
        # Register with Traefik
        await self._register_with_traefik(service_info)
        
        # Store service info
        self._services[registration.name] = service_info
        
        logger.info(
            "Service registered successfully",
            service_name=registration.name,
            service_url=service_url,
            internal_port=registration.internal_port
        )
        
        # Start health monitoring if this is the first service
        if len(self._services) == 1:
            await self._start_health_monitoring()
        
        return service_url

    async def deregister_service(self, service_name: str) -> bool:
        """Deregister a service from the URL manager.
        
        Args:
            service_name: Name of the service to deregister
            
        Returns:
            bool: True if service was deregistered, False if not found
        """
        if service_name not in self._services:
            return False

        service_info = self._services[service_name]
        
        # Deregister from Traefik
        await self._deregister_from_traefik(service_info)
        
        # Remove from local registry
        del self._services[service_name]
        
        logger.info(
            "Service deregistered successfully",
            service_name=service_name,
            service_url=service_info.url
        )
        
        # Stop health monitoring if no services remain
        if not self._services and self._health_check_task:
            self._health_check_task.cancel()
            self._health_check_task = None
        
        return True

    def get_service_url(self, service_name: str) -> Optional[str]:
        """Get the URL for a registered service.
        
        Args:
            service_name: Name of the service
            
        Returns:
            Optional[str]: Service URL if found, None otherwise
        """
        service_info = self._services.get(service_name)
        return service_info.url if service_info else None

    def list_services(self) -> List[Dict[str, any]]:
        """List all registered services.
        
        Returns:
            List[Dict]: List of service information dictionaries
        """
        return [asdict(service_info) for service_info in self._services.values()]

    def get_service_info(self, service_name: str) -> Optional[ServiceInfo]:
        """Get detailed information about a service.
        
        Args:
            service_name: Name of the service
            
        Returns:
            Optional[ServiceInfo]: Service information if found
        """
        return self._services.get(service_name)

    def _generate_service_url(self, service_name: str) -> str:
        """Generate a service URL using the *.pitas.localhost pattern.
        
        Args:
            service_name: Name of the service
            
        Returns:
            str: Generated service URL
        """
        # Convert service name to URL-safe format
        url_safe_name = service_name.lower().replace('_', '-').replace(' ', '-')
        return f"http://{url_safe_name}.{settings.service_domain}"

    async def _register_with_traefik(self, service_info: ServiceInfo) -> None:
        """Register service with Traefik using labels/configuration.
        
        Args:
            service_info: Service information to register
        """
        # This would typically involve updating Traefik configuration
        # For now, we'll log the registration details
        logger.info(
            "Registering service with Traefik",
            service_name=service_info.name,
            service_url=service_info.url,
            internal_port=service_info.internal_port,
            traefik_rule=f"Host(`{service_info.name.lower().replace('_', '-')}.{settings.service_domain}`)"
        )

    async def _deregister_from_traefik(self, service_info: ServiceInfo) -> None:
        """Deregister service from Traefik.
        
        Args:
            service_info: Service information to deregister
        """
        logger.info(
            "Deregistering service from Traefik",
            service_name=service_info.name,
            service_url=service_info.url
        )

    async def _start_health_monitoring(self) -> None:
        """Start the health monitoring background task."""
        if self._health_check_task is None or self._health_check_task.done():
            self._health_check_task = asyncio.create_task(self._health_monitor_loop())
            logger.info("Started health monitoring for registered services")

    async def _health_monitor_loop(self) -> None:
        """Background task to monitor service health."""
        while self._services:
            try:
                await self._check_all_services_health()
                await asyncio.sleep(self._health_check_interval)
            except asyncio.CancelledError:
                logger.info("Health monitoring cancelled")
                break
            except Exception as e:
                logger.error("Error in health monitoring loop", error=str(e))
                await asyncio.sleep(self._health_check_interval)

    async def _check_all_services_health(self) -> None:
        """Check health of all registered services."""
        for service_name, service_info in self._services.items():
            try:
                await self._check_service_health(service_info)
            except Exception as e:
                logger.error(
                    "Failed to check service health",
                    service_name=service_name,
                    error=str(e)
                )

    async def _check_service_health(self, service_info: ServiceInfo) -> None:
        """Check health of a specific service.
        
        Args:
            service_info: Service to check
        """
        health_url = f"http://localhost:{service_info.internal_port}{service_info.health_endpoint}"
        
        try:
            response = await self._client.get(health_url)
            is_healthy = response.status_code == 200
            
            service_info.is_healthy = is_healthy
            service_info.last_health_check = datetime.utcnow()
            
            if not is_healthy:
                logger.warning(
                    "Service health check failed",
                    service_name=service_info.name,
                    status_code=response.status_code,
                    health_url=health_url
                )
        except Exception as e:
            service_info.is_healthy = False
            service_info.last_health_check = datetime.utcnow()
            logger.error(
                "Service health check error",
                service_name=service_info.name,
                error=str(e),
                health_url=health_url
            )

    async def close(self) -> None:
        """Clean up resources."""
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
        
        await self._client.aclose()
        logger.info("Service URL Manager closed")


# Global instance
service_url_manager = ServiceURLManager()
