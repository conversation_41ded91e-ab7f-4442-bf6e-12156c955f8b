"""Main FastAPI application module."""

import structlog
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

from pitas.api.v1.router import api_router
from pitas.core.config import settings
from pitas.core.exceptions import AppException
from pitas.core.logging import setup_logging
from pitas.core.cache import startup_cache, shutdown_cache
from pitas.core.monitoring import startup_monitoring, shutdown_monitoring
from pitas.core.middleware import (
    PerformanceMiddleware,
    CacheMiddleware,
    CompressionMiddleware,
    RateLimitMiddleware
)
from pitas.db.neo4j import startup_neo4j, shutdown_neo4j
from pitas.db.influxdb import startup_influxdb, shutdown_influxdb
from pitas.core.service_url_manager import service_url_manager, ServiceRegistration

# Configure structured logging
setup_logging()
logger = structlog.get_logger(__name__)

# Rate limiter
limiter = Limiter(key_func=get_remote_address)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan events.

    Args:
        app: FastAPI application instance

    Yields:
        None: During application runtime
    """
    # Startup
    logger.info("Application starting up", version=settings.project_version)

    # Initialize Phase 11: Performance monitoring and caching
    try:
        logger.info("Initializing cache connection...")
        await startup_cache()
        logger.info("Cache connection established")
    except Exception as e:
        logger.warning("Failed to connect to cache", error=str(e))

    try:
        logger.info("Initializing performance monitoring...")
        await startup_monitoring()
        logger.info("Performance monitoring started")
    except Exception as e:
        logger.warning("Failed to start performance monitoring", error=str(e))

    # Initialize Phase 3 databases
    try:
        logger.info("Initializing Neo4j connection...")
        await startup_neo4j()
        logger.info("Neo4j connection established")
    except Exception as e:
        logger.warning("Failed to connect to Neo4j", error=str(e))

    try:
        logger.info("Initializing InfluxDB connection...")
        await startup_influxdb()
        logger.info("InfluxDB connection established")
    except Exception as e:
        logger.warning("Failed to connect to InfluxDB", error=str(e))

    # Initialize Service URL Manager and register core services
    if settings.service_registry_enabled:
        try:
            logger.info("Initializing Service URL Manager...")

            # Register the main API service
            api_registration = ServiceRegistration(
                name="pitas-api",
                internal_port=8000,
                health_endpoint="/health",
                tags=["api", "core"],
                metadata={
                    "version": settings.project_version,
                    "environment": settings.environment
                }
            )
            await service_url_manager.register_service(api_registration)
            logger.info("Service URL Manager initialized and API service registered")
        except Exception as e:
            logger.warning("Failed to initialize Service URL Manager", error=str(e))

    yield

    # Shutdown
    logger.info("Application shutting down")

    # Cleanup Phase 3 databases
    try:
        await shutdown_neo4j()
        logger.info("Neo4j connection closed")
    except Exception as e:
        logger.warning("Error closing Neo4j connection", error=str(e))

    try:
        shutdown_influxdb()
        logger.info("InfluxDB connection closed")
    except Exception as e:
        logger.warning("Error closing InfluxDB connection", error=str(e))

    try:
        await shutdown_monitoring()
        logger.info("Performance monitoring stopped")
    except Exception as e:
        logger.warning("Error stopping performance monitoring", error=str(e))

    try:
        await shutdown_cache()
        logger.info("Cache connection closed")
    except Exception as e:
        logger.warning("Error closing cache connection", error=str(e))

    # Cleanup Service URL Manager
    if settings.service_registry_enabled:
        try:
            await service_url_manager.close()
            logger.info("Service URL Manager closed")
        except Exception as e:
            logger.warning("Error closing Service URL Manager", error=str(e))


def create_app() -> FastAPI:
    """Create and configure FastAPI application.

    Returns:
        FastAPI: Configured application instance
    """
    app = FastAPI(
        title=settings.project_name,
        version=settings.project_version,
        description=settings.description,
        openapi_url=f"{settings.api_v1_str}/openapi.json",
        docs_url=f"{settings.api_v1_str}/docs",
        redoc_url=f"{settings.api_v1_str}/redoc",
        lifespan=lifespan,
    )

    # Add performance optimization middleware
    app.add_middleware(PerformanceMiddleware)
    app.add_middleware(CacheMiddleware)
    app.add_middleware(CompressionMiddleware)
    app.add_middleware(RateLimitMiddleware)

    # Add rate limiting
    app.state.limiter = limiter
    app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

    # Set up CORS
    if settings.backend_cors_origins:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=[str(origin) for origin in settings.backend_cors_origins],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

    # Include routers
    app.include_router(api_router, prefix=settings.api_v1_str)

    # Global exception handler
    @app.exception_handler(AppException)
    async def app_exception_handler(
        request: Request,
        exc: AppException,
    ) -> JSONResponse:
        """Handle application exceptions.

        Args:
            request: HTTP request
            exc: Application exception

        Returns:
            JSONResponse with error details
        """
        logger.error(
            "Application exception occurred",
            exception=exc.__class__.__name__,
            message=exc.message,
            details=exc.details,
            path=request.url.path,
        )

        return JSONResponse(
            status_code=500,
            content={
                "message": exc.message,
                "details": exc.details,
            },
        )

    return app


# Create application instance
app = create_app()


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "pitas.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
    )