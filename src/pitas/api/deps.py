"""Common dependencies for API endpoints."""

from typing import As<PERSON><PERSON>enerator, Optional
from uuid import UUID

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from pitas.db.session import get_db
from pitas.db.models.user import User
from pitas.core.security import verify_token
from pitas.core.config import settings

# Security scheme
security = HTTPBearer()


async def get_current_user_id(
    token: str = Depends(verify_token),
) -> str:
    """Get current authenticated user ID.

    Args:
        token: Verified JWT token subject

    Returns:
        str: User ID from token
    """
    return token


async def get_current_user(
    user_id: str = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> User:
    """Get current authenticated user.

    Args:
        user_id: User ID from verified JWT token
        db: Database session

    Returns:
        Current user

    Raises:
        HTTPException: If user not found
    """
    try:
        from uuid import UUID
        user_uuid = UUID(user_id)

        result = await db.execute(
            select(User).where(User.id == user_uuid)
        )
        user = result.scalar_one_or_none()

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Inactive user"
            )

        return user

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid user ID format",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_admin_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """Get current user if they have admin privileges.

    Args:
        current_user: Current authenticated user

    Returns:
        User: Admin user

    Raises:
        HTTPException: If user is not admin
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return current_user


async def get_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """Get current user if they are active.

    Args:
        current_user: Current authenticated user

    Returns:
        User: Active user

    Raises:
        HTTPException: If user is not active
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user


def get_rate_limiter():
    """Get rate limiter for endpoints."""
    from slowapi import Limiter
    from slowapi.util import get_remote_address

    return Limiter(
        key_func=get_remote_address,
        default_limits=[f"{settings.rate_limit_per_minute}/minute"]
    )