"""Health check endpoints."""

import asyncio
import time
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from pitas.core.config import settings
from pitas.db.session import get_db
from pitas.schemas.base import HealthCheck

router = APIRouter()


@router.get("/health", response_model=HealthCheck)
async def health_check() -> HealthCheck:
    """Basic health check endpoint.

    Returns:
        HealthCheck: Service health status
    """
    return HealthCheck(
        status="healthy",
        timestamp=datetime.utcnow(),
        version=settings.project_version,
        environment=settings.environment,
    )


@router.get("/health/detailed", response_model=dict)
async def detailed_health_check(
    db: AsyncSession = Depends(get_db),
) -> dict:
    """Detailed health check with dependency status.

    Args:
        db: Database session

    Returns:
        dict: Detailed health status
    """
    health_status = {
        "status": "healthy",
        "timestamp": datetime.utcnow(),
        "version": settings.project_version,
        "environment": settings.environment,
        "dependencies": {}
    }

    # Check database connectivity
    try:
        start_time = time.time()
        await db.execute(text("SELECT 1"))
        response_time = (time.time() - start_time) * 1000
        health_status["dependencies"]["database"] = {
            "status": "healthy",
            "response_time_ms": round(response_time, 2)
        }
    except Exception as e:
        health_status["status"] = "unhealthy"
        health_status["dependencies"]["database"] = {
            "status": "unhealthy",
            "error": str(e)
        }

    # Check Redis connectivity
    health_status["dependencies"]["redis"] = await _check_redis()

    # Check Neo4j connectivity
    health_status["dependencies"]["neo4j"] = await _check_neo4j()

    # Check InfluxDB connectivity
    health_status["dependencies"]["influxdb"] = await _check_influxdb()

    # Update overall status based on dependencies
    if any(dep.get("status") == "unhealthy" for dep in health_status["dependencies"].values()):
        health_status["status"] = "unhealthy"

    return health_status


async def _check_redis() -> Dict[str, Any]:
    """Check Redis connectivity."""
    try:
        import redis.asyncio as redis
        start_time = time.time()

        # Parse Redis URL
        redis_client = redis.from_url(settings.redis_url)
        await redis_client.ping()
        response_time = (time.time() - start_time) * 1000
        await redis_client.close()

        return {
            "status": "healthy",
            "response_time_ms": round(response_time, 2)
        }
    except ImportError:
        return {
            "status": "unavailable",
            "error": "Redis client not installed"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }


async def _check_neo4j() -> Dict[str, Any]:
    """Check Neo4j connectivity."""
    try:
        from neo4j import AsyncGraphDatabase
        start_time = time.time()

        driver = AsyncGraphDatabase.driver(
            settings.neo4j_uri,
            auth=(settings.neo4j_user, settings.neo4j_password)
        )

        async with driver.session() as session:
            await session.run("RETURN 1")

        response_time = (time.time() - start_time) * 1000
        await driver.close()

        return {
            "status": "healthy",
            "response_time_ms": round(response_time, 2)
        }
    except ImportError:
        return {
            "status": "unavailable",
            "error": "Neo4j driver not installed"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }


async def _check_influxdb() -> Dict[str, Any]:
    """Check InfluxDB connectivity."""
    try:
        from influxdb_client.client.influxdb_client_async import InfluxDBClientAsync
        start_time = time.time()

        async with InfluxDBClientAsync(
            url=settings.influxdb_url,
            token=settings.influxdb_token,
            org=settings.influxdb_org
        ) as client:
            health = await client.health()

        response_time = (time.time() - start_time) * 1000

        return {
            "status": "healthy" if health.status == "pass" else "unhealthy",
            "response_time_ms": round(response_time, 2),
            "version": getattr(health, 'version', 'unknown')
        }
    except ImportError:
        return {
            "status": "unavailable",
            "error": "InfluxDB client not installed"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }


@router.get("/ready")
async def readiness_check(
    db: AsyncSession = Depends(get_db),
) -> dict:
    """Kubernetes readiness probe endpoint.

    Args:
        db: Database session

    Returns:
        dict: Readiness status
    """
    try:
        await db.execute(text("SELECT 1"))
        return {"status": "ready"}
    except Exception:
        return {"status": "not ready"}


@router.get("/live")
async def liveness_check() -> dict:
    """Kubernetes liveness probe endpoint.

    Returns:
        dict: Liveness status
    """
    return {"status": "alive"}