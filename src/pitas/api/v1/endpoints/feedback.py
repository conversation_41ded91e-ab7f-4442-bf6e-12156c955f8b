"""Feedback collection and analytics API endpoints."""

from datetime import datetime, timed<PERSON>ta
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from pitas.api.deps import get_current_user_id, get_db
from pitas.db.models.feedback import FeedbackStatus
from pitas.schemas.feedback import (
    UserFeedback, UserFeedbackCreate, UserFeedbackUpdate,
    FeedbackResponse, FeedbackResponseCreate,
    UserBehaviorAnalytics, UserBehaviorAnalyticsCreate,
    FeatureUsageMetrics, FeatureUsageMetricsCreate,
    SatisfactionSurvey, SatisfactionSurveyCreate,
    FeedbackAnalytics, UserEngagementMetrics, FeedbackSummary
)
from pitas.services.feedback import FeedbackService

router = APIRouter()


@router.post("/", response_model=UserFeedback)
async def create_feedback(
    feedback_data: UserFeedbackCreate,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> UserFeedback:
    """Create new user feedback."""
    service = FeedbackService(db)
    return await service.create_feedback(current_user_id, feedback_data)


@router.get("/", response_model=List[UserFeedback])
async def list_feedback(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    feedback_type: Optional[str] = None,
    status: Optional[FeedbackStatus] = None,
    assigned_to_me: bool = False,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> List[UserFeedback]:
    """List user feedback with filtering options."""
    service = FeedbackService(db)
    
    filters = {}
    if feedback_type:
        filters["feedback_type"] = feedback_type
    if status:
        filters["status"] = status
    if assigned_to_me:
        filters["assigned_to"] = current_user_id
    
    return await service.get_multi(skip=skip, limit=limit, **filters)


@router.get("/{feedback_id}", response_model=UserFeedback)
async def get_feedback(
    feedback_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> UserFeedback:
    """Get specific feedback by ID."""
    service = FeedbackService(db)
    feedback = await service.get(feedback_id)
    
    if not feedback:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Feedback not found"
        )
    
    return feedback


@router.put("/{feedback_id}", response_model=UserFeedback)
async def update_feedback(
    feedback_id: UUID,
    feedback_data: UserFeedbackUpdate,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> UserFeedback:
    """Update feedback (admin only)."""
    service = FeedbackService(db)
    
    # Check if feedback exists
    feedback = await service.get(feedback_id)
    if not feedback:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Feedback not found"
        )
    
    return await service.update(feedback_id, feedback_data)


@router.post("/{feedback_id}/responses", response_model=FeedbackResponse)
async def respond_to_feedback(
    feedback_id: UUID,
    response_data: FeedbackResponseCreate,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> FeedbackResponse:
    """Add response to feedback."""
    service = FeedbackService(db)
    
    # Ensure feedback_id matches
    response_data.feedback_id = feedback_id
    
    return await service.respond_to_feedback(
        feedback_id, current_user_id, response_data
    )


@router.put("/{feedback_id}/status", response_model=UserFeedback)
async def update_feedback_status(
    feedback_id: UUID,
    status: FeedbackStatus,
    assigned_to: Optional[UUID] = None,
    resolution_notes: Optional[str] = None,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> UserFeedback:
    """Update feedback status (admin only)."""
    service = FeedbackService(db)
    
    return await service.update_feedback_status(
        feedback_id, status, assigned_to, resolution_notes
    )


@router.post("/behavior", response_model=UserBehaviorAnalytics)
async def track_user_behavior(
    analytics_data: UserBehaviorAnalyticsCreate,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> UserBehaviorAnalytics:
    """Track user behavior for analytics."""
    service = FeedbackService(db)
    return await service.track_user_behavior(current_user_id, analytics_data)


@router.post("/feature-usage", response_model=FeatureUsageMetrics)
async def update_feature_usage(
    feature_name: str,
    usage_data: FeatureUsageMetricsCreate,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> FeatureUsageMetrics:
    """Update feature usage metrics."""
    service = FeedbackService(db)
    return await service.update_feature_usage(current_user_id, feature_name, usage_data)


@router.get("/feature-usage/", response_model=List[FeatureUsageMetrics])
async def get_feature_usage_metrics(
    feature_name: Optional[str] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> List[FeatureUsageMetrics]:
    """Get feature usage metrics."""
    service = FeedbackService(db)
    
    filters = {"user_id": current_user_id}
    if feature_name:
        filters["feature_name"] = feature_name
    
    return await service.get_multi(skip=skip, limit=limit, **filters)


@router.post("/surveys", response_model=SatisfactionSurvey)
async def create_satisfaction_survey(
    survey_data: SatisfactionSurveyCreate,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> SatisfactionSurvey:
    """Create satisfaction survey response."""
    service = FeedbackService(db)
    return await service.create_satisfaction_survey(current_user_id, survey_data)


@router.get("/surveys/", response_model=List[SatisfactionSurvey])
async def list_satisfaction_surveys(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    survey_type: Optional[str] = None,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> List[SatisfactionSurvey]:
    """List satisfaction surveys."""
    service = FeedbackService(db)
    
    filters = {}
    if survey_type:
        filters["survey_type"] = survey_type
    
    return await service.get_multi(skip=skip, limit=limit, **filters)


@router.get("/analytics/overview", response_model=FeedbackAnalytics)
async def get_feedback_analytics(
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> FeedbackAnalytics:
    """Get feedback analytics overview."""
    service = FeedbackService(db)
    
    if not start_date:
        start_date = datetime.utcnow() - timedelta(days=30)
    if not end_date:
        end_date = datetime.utcnow()
    
    return await service.get_feedback_analytics(start_date, end_date)


@router.get("/analytics/engagement", response_model=UserEngagementMetrics)
async def get_user_engagement_metrics(
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> UserEngagementMetrics:
    """Get user engagement metrics."""
    # This would be implemented with more complex analytics
    # For now, return sample data
    return UserEngagementMetrics(
        total_users=100,
        active_users_last_30_days=85,
        average_session_duration=1800.0,  # 30 minutes
        page_views_per_session=12.5,
        bounce_rate=25.0,
        feature_adoption_rates={
            "dashboard": 95.0,
            "reports": 78.0,
            "analytics": 65.0,
            "settings": 45.0
        },
        user_journey_analytics={}
    )


@router.get("/my-feedback", response_model=List[UserFeedback])
async def get_my_feedback(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> List[UserFeedback]:
    """Get current user's feedback."""
    service = FeedbackService(db)
    return await service.get_multi(
        skip=skip, 
        limit=limit, 
        user_id=current_user_id
    )


@router.get("/assigned-to-me", response_model=List[UserFeedback])
async def get_assigned_feedback(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> List[UserFeedback]:
    """Get feedback assigned to current user."""
    service = FeedbackService(db)
    return await service.get_multi(
        skip=skip, 
        limit=limit, 
        assigned_to=current_user_id
    )


@router.get("/summary", response_model=FeedbackSummary)
async def get_feedback_summary(
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> FeedbackSummary:
    """Get comprehensive feedback summary."""
    service = FeedbackService(db)
    
    # Get analytics
    analytics = await service.get_feedback_analytics()
    
    # Get engagement metrics (simplified)
    engagement_metrics = UserEngagementMetrics(
        total_users=100,
        active_users_last_30_days=85,
        average_session_duration=1800.0,
        page_views_per_session=12.5,
        bounce_rate=25.0,
        feature_adoption_rates={},
        user_journey_analytics={}
    )
    
    # Get recent feedback
    recent_feedback = await service.get_multi(limit=10)
    
    return FeedbackSummary(
        analytics=analytics,
        engagement_metrics=engagement_metrics,
        trends={
            "period": "monthly",
            "feedback_volume": [],
            "satisfaction_trends": [],
            "nps_trends": [],
            "resolution_time_trends": []
        },
        recent_feedback=recent_feedback,
        improvement_opportunities=[
            "Improve response time to user feedback",
            "Enhance mobile user experience",
            "Simplify onboarding process"
        ]
    )
