"""Service management API endpoints.

This module provides REST API endpoints for managing services through the
serviceURLmanager, including registration, deregistration, and monitoring.
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, status, Depends
from pydantic import BaseModel, Field
import structlog

from pitas.core.service_url_manager import (
    service_url_manager,
    ServiceRegistration,
    ServiceInfo
)
from pitas.core.config import settings


logger = structlog.get_logger(__name__)
router = APIRouter()


class ServiceRegistrationResponse(BaseModel):
    """Response model for service registration."""
    service_name: str = Field(..., description="Name of the registered service")
    service_url: str = Field(..., description="Generated service URL")
    message: str = Field(..., description="Success message")


class ServiceListResponse(BaseModel):
    """Response model for service listing."""
    services: List[Dict[str, Any]] = Field(..., description="List of registered services")
    total_count: int = Field(..., description="Total number of services")


class ServiceHealthResponse(BaseModel):
    """Response model for service health status."""
    service_name: str = Field(..., description="Name of the service")
    is_healthy: bool = Field(..., description="Health status")
    last_health_check: Optional[str] = Field(None, description="Last health check timestamp")
    service_url: str = Field(..., description="Service URL")


class ServiceDeregistrationResponse(BaseModel):
    """Response model for service deregistration."""
    service_name: str = Field(..., description="Name of the deregistered service")
    message: str = Field(..., description="Success message")


@router.post(
    "/register",
    response_model=ServiceRegistrationResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Register a new service",
    description="Register a new service with the service URL manager and Traefik"
)
async def register_service(registration: ServiceRegistration) -> ServiceRegistrationResponse:
    """Register a new service with the service URL manager.
    
    Args:
        registration: Service registration information
        
    Returns:
        ServiceRegistrationResponse: Registration confirmation with service URL
        
    Raises:
        HTTPException: If service is already registered or registration fails
    """
    try:
        service_url = await service_url_manager.register_service(registration)
        
        logger.info(
            "Service registered via API",
            service_name=registration.name,
            service_url=service_url
        )
        
        return ServiceRegistrationResponse(
            service_name=registration.name,
            service_url=service_url,
            message=f"Service '{registration.name}' registered successfully"
        )
    
    except ValueError as e:
        logger.warning(
            "Service registration failed - already exists",
            service_name=registration.name,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    
    except Exception as e:
        logger.error(
            "Service registration failed",
            service_name=registration.name,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to register service: {str(e)}"
        )


@router.delete(
    "/{service_name}",
    response_model=ServiceDeregistrationResponse,
    summary="Deregister a service",
    description="Remove a service from the service URL manager and Traefik"
)
async def deregister_service(service_name: str) -> ServiceDeregistrationResponse:
    """Deregister a service from the service URL manager.
    
    Args:
        service_name: Name of the service to deregister
        
    Returns:
        ServiceDeregistrationResponse: Deregistration confirmation
        
    Raises:
        HTTPException: If service is not found
    """
    try:
        success = await service_url_manager.deregister_service(service_name)
        
        if not success:
            logger.warning(
                "Service deregistration failed - not found",
                service_name=service_name
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Service '{service_name}' not found"
            )
        
        logger.info(
            "Service deregistered via API",
            service_name=service_name
        )
        
        return ServiceDeregistrationResponse(
            service_name=service_name,
            message=f"Service '{service_name}' deregistered successfully"
        )
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(
            "Service deregistration failed",
            service_name=service_name,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to deregister service: {str(e)}"
        )


@router.get(
    "/",
    response_model=ServiceListResponse,
    summary="List all registered services",
    description="Get a list of all services registered with the service URL manager"
)
async def list_services() -> ServiceListResponse:
    """List all registered services.
    
    Returns:
        ServiceListResponse: List of all registered services
    """
    try:
        services = service_url_manager.list_services()
        
        logger.debug(
            "Services listed via API",
            service_count=len(services)
        )
        
        return ServiceListResponse(
            services=services,
            total_count=len(services)
        )
    
    except Exception as e:
        logger.error(
            "Failed to list services",
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list services: {str(e)}"
        )


@router.get(
    "/{service_name}",
    response_model=Dict[str, Any],
    summary="Get service information",
    description="Get detailed information about a specific service"
)
async def get_service_info(service_name: str) -> Dict[str, Any]:
    """Get detailed information about a specific service.
    
    Args:
        service_name: Name of the service
        
    Returns:
        Dict[str, Any]: Service information
        
    Raises:
        HTTPException: If service is not found
    """
    try:
        service_info = service_url_manager.get_service_info(service_name)
        
        if not service_info:
            logger.warning(
                "Service info request failed - not found",
                service_name=service_name
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Service '{service_name}' not found"
            )
        
        logger.debug(
            "Service info retrieved via API",
            service_name=service_name
        )
        
        # Convert ServiceInfo to dict for JSON serialization
        from dataclasses import asdict
        return asdict(service_info)
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(
            "Failed to get service info",
            service_name=service_name,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get service info: {str(e)}"
        )


@router.get(
    "/{service_name}/health",
    response_model=ServiceHealthResponse,
    summary="Get service health status",
    description="Get the current health status of a specific service"
)
async def get_service_health(service_name: str) -> ServiceHealthResponse:
    """Get the health status of a specific service.
    
    Args:
        service_name: Name of the service
        
    Returns:
        ServiceHealthResponse: Service health information
        
    Raises:
        HTTPException: If service is not found
    """
    try:
        service_info = service_url_manager.get_service_info(service_name)
        
        if not service_info:
            logger.warning(
                "Service health request failed - not found",
                service_name=service_name
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Service '{service_name}' not found"
            )
        
        logger.debug(
            "Service health retrieved via API",
            service_name=service_name,
            is_healthy=service_info.is_healthy
        )
        
        return ServiceHealthResponse(
            service_name=service_name,
            is_healthy=service_info.is_healthy,
            last_health_check=service_info.last_health_check.isoformat() if service_info.last_health_check else None,
            service_url=service_info.url
        )
    
    except HTTPException:
        raise
    
    except Exception as e:
        logger.error(
            "Failed to get service health",
            service_name=service_name,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get service health: {str(e)}"
        )


@router.get(
    "/health/summary",
    response_model=Dict[str, Any],
    summary="Get health summary for all services",
    description="Get a summary of health status for all registered services"
)
async def get_services_health_summary() -> Dict[str, Any]:
    """Get health summary for all registered services.
    
    Returns:
        Dict[str, Any]: Health summary information
    """
    try:
        services = service_url_manager.list_services()
        
        healthy_count = sum(1 for service in services if service.get('is_healthy', False))
        unhealthy_count = len(services) - healthy_count
        
        health_summary = {
            "total_services": len(services),
            "healthy_services": healthy_count,
            "unhealthy_services": unhealthy_count,
            "health_percentage": (healthy_count / len(services) * 100) if services else 100,
            "services": [
                {
                    "name": service["name"],
                    "url": service["url"],
                    "is_healthy": service.get("is_healthy", False),
                    "last_health_check": service.get("last_health_check")
                }
                for service in services
            ]
        }
        
        logger.debug(
            "Services health summary retrieved via API",
            total_services=len(services),
            healthy_services=healthy_count,
            unhealthy_services=unhealthy_count
        )
        
        return health_summary
    
    except Exception as e:
        logger.error(
            "Failed to get services health summary",
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get services health summary: {str(e)}"
        )
