"""User management endpoints."""

from typing import Any, List
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from pitas.api.deps import get_db, get_current_user, get_admin_user, get_active_user
from pitas.db.models.user import User, UserRole
from pitas.schemas.user import (
    User as UserSchema,
    UserCreate,
    UserUpdate,
    UserPasswordUpdate,
    UserListResponse,
    UserSearchFilters,
    UserWithRelations
)
from pitas.services.user import UserService

router = APIRouter()


@router.get("/", response_model=List[UserSchema])
async def list_users(
    skip: int = Query(0, ge=0, description="Number of users to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of users to return"),
    role: UserRole = Query(None, description="Filter by user role"),
    is_active: bool = Query(None, description="Filter by active status"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_admin_user)
) -> Any:
    """List all users (admin only)."""
    user_service = UserService()

    if role is not None:
        users = await user_service.get_active_users(db, role=role)
    else:
        # Get all users with pagination
        users = await user_service.get_multi(db, skip=skip, limit=limit)

    if is_active is not None:
        users = [user for user in users if user.is_active == is_active]

    return users


@router.post("/", response_model=UserSchema)
async def create_user(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_admin_user)
) -> Any:
    """Create a new user (admin only)."""
    user_service = UserService()

    try:
        user = await user_service.create_user(db=db, user_data=user_data)
        await db.commit()
        return user
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/me", response_model=UserSchema)
async def get_current_user_profile(
    current_user: User = Depends(get_current_user)
) -> Any:
    """Get current user profile."""
    return current_user


@router.put("/me", response_model=UserSchema)
async def update_current_user(
    user_update: UserUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """Update current user profile."""
    user_service = UserService()

    try:
        updated_user = await user_service.update(
            db=db,
            db_obj=current_user,
            obj_in=user_update
        )
        await db.commit()
        return updated_user
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put("/me/password")
async def update_current_user_password(
    password_update: UserPasswordUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Any:
    """Update current user password."""
    user_service = UserService()

    # Verify current password
    if not await user_service.authenticate(
        db=db,
        email=current_user.email,
        password=password_update.current_password
    ):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Incorrect current password"
        )

    try:
        await user_service.update_password(
            db=db,
            user_id=current_user.id,
            new_password=password_update.new_password
        )
        await db.commit()
        return {"message": "Password updated successfully"}
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/{user_id}", response_model=UserSchema)
async def get_user(
    user_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_admin_user)
) -> Any:
    """Get user by ID (admin only)."""
    user_service = UserService()
    user = await user_service.get(db, user_id)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    return user


@router.put("/{user_id}", response_model=UserSchema)
async def update_user(
    user_id: UUID,
    user_update: UserUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_admin_user)
) -> Any:
    """Update user by ID (admin only)."""
    user_service = UserService()
    user = await user_service.get(db, user_id)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    try:
        updated_user = await user_service.update(
            db=db,
            db_obj=user,
            obj_in=user_update
        )
        await db.commit()
        return updated_user
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/{user_id}")
async def deactivate_user(
    user_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_admin_user)
) -> Any:
    """Deactivate user by ID (admin only)."""
    user_service = UserService()

    try:
        await user_service.deactivate_user(db=db, user_id=user_id)
        await db.commit()
        return {"message": "User deactivated successfully"}
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put("/{user_id}/role")
async def update_user_role(
    user_id: UUID,
    new_role: UserRole,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_admin_user)
) -> Any:
    """Update user role (admin only)."""
    user_service = UserService()

    try:
        updated_user = await user_service.update_user_role(
            db=db,
            user_id=user_id,
            new_role=new_role
        )
        await db.commit()
        return updated_user
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )