"""Career development and employee retention API endpoints."""

from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from pitas.api.deps import get_current_user_id, get_db
from pitas.db.models.career import CareerTier, CareerTrack, RecognitionType, WorkLifeBalanceStatus
from pitas.schemas.career import (
    CareerProfile, CareerProfileCreate, CareerProfileUpdate,
    EmployeeRecognition, EmployeeRecognitionCreate, EmployeeRecognitionUpdate,
    WorkLifeBalanceMetric, WorkLifeBalanceMetricCreate, WorkLifeBalanceMetricUpdate,
    CareerDashboard, CareerMetrics
)
from pitas.services.career import (
    CareerProfileService, EmployeeRecognitionService, 
    WorkLifeBalanceService, CareerAnalyticsService
)

router = APIRouter()


@router.post("/profiles", response_model=CareerProfile)
async def create_career_profile(
    profile_data: CareerProfileCreate,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> CareerProfile:
    """Create a new career profile for an employee."""
    service = CareerProfileService()
    try:
        return await service.create_career_profile(db, profile_data=profile_data)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/profiles", response_model=List[CareerProfile])
async def list_career_profiles(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    tier: Optional[CareerTier] = None,
    track: Optional[CareerTrack] = None,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> List[CareerProfile]:
    """List career profiles with filtering options."""
    service = CareerProfileService()
    
    if tier:
        return await service.get_employees_by_tier(db, tier)
    
    # TODO: Add track filtering and pagination
    return await service.get_multi(db, skip=skip, limit=limit)


@router.get("/profiles/{profile_id}", response_model=CareerProfile)
async def get_career_profile(
    profile_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> CareerProfile:
    """Get specific career profile."""
    service = CareerProfileService()
    profile = await service.get(db, profile_id)
    
    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Career profile not found"
        )
    
    return profile


@router.get("/profiles/employee/{employee_id}", response_model=CareerProfile)
async def get_career_profile_by_employee(
    employee_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> CareerProfile:
    """Get career profile by employee ID."""
    service = CareerProfileService()
    profile = await service.get_by_employee_id(db, employee_id)
    
    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Career profile not found for employee"
        )
    
    return profile


@router.put("/profiles/{profile_id}", response_model=CareerProfile)
async def update_career_profile(
    profile_id: UUID,
    profile_data: CareerProfileUpdate,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> CareerProfile:
    """Update career profile."""
    service = CareerProfileService()
    
    profile = await service.get(db, profile_id)
    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Career profile not found"
        )
    
    return await service.update(db, db_obj=profile, obj_in=profile_data)


@router.post("/profiles/{profile_id}/calculate-readiness", response_model=dict)
async def calculate_promotion_readiness(
    profile_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """Calculate promotion readiness score for an employee."""
    service = CareerProfileService()
    
    try:
        score = await service.calculate_promotion_readiness(db, profile_id)
        return {"profile_id": profile_id, "promotion_readiness_score": score}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.get("/profiles/promotion-ready", response_model=List[CareerProfile])
async def get_promotion_ready_employees(
    threshold: float = Query(80.0, ge=0.0, le=100.0),
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> List[CareerProfile]:
    """Get employees ready for promotion."""
    service = CareerProfileService()
    return await service.get_promotion_ready_employees(db, threshold=threshold)


@router.post("/recognitions", response_model=EmployeeRecognition)
async def create_employee_recognition(
    recognition_data: EmployeeRecognitionCreate,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> EmployeeRecognition:
    """Create a new employee recognition."""
    service = EmployeeRecognitionService()
    
    try:
        return await service.create_recognition(
            db, 
            recognition_data=recognition_data,
            nominated_by=current_user_id
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/recognitions", response_model=List[EmployeeRecognition])
async def list_recognitions(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    employee_profile_id: Optional[UUID] = None,
    recognition_type: Optional[RecognitionType] = None,
    days: int = Query(30, ge=1, le=365),
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> List[EmployeeRecognition]:
    """List employee recognitions with filtering options."""
    service = EmployeeRecognitionService()
    
    if employee_profile_id:
        return await service.get_employee_recognitions(
            db, 
            employee_profile_id,
            recognition_type=recognition_type,
            limit=limit
        )
    
    return await service.get_recent_recognitions(db, days=days, limit=limit)


@router.get("/recognitions/{recognition_id}", response_model=EmployeeRecognition)
async def get_recognition(
    recognition_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> EmployeeRecognition:
    """Get specific employee recognition."""
    service = EmployeeRecognitionService()
    recognition = await service.get(db, recognition_id)
    
    if not recognition:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Recognition not found"
        )
    
    return recognition


@router.put("/recognitions/{recognition_id}", response_model=EmployeeRecognition)
async def update_recognition(
    recognition_id: UUID,
    recognition_data: EmployeeRecognitionUpdate,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> EmployeeRecognition:
    """Update employee recognition."""
    service = EmployeeRecognitionService()
    
    recognition = await service.get(db, recognition_id)
    if not recognition:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Recognition not found"
        )
    
    return await service.update(db, db_obj=recognition, obj_in=recognition_data)


@router.post("/recognitions/{recognition_id}/approve", response_model=EmployeeRecognition)
async def approve_recognition(
    recognition_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> EmployeeRecognition:
    """Approve an employee recognition."""
    service = EmployeeRecognitionService()
    
    try:
        return await service.approve_recognition(db, recognition_id, current_user_id)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.post("/work-life-balance", response_model=WorkLifeBalanceMetric)
async def record_work_life_balance_metrics(
    metric_data: WorkLifeBalanceMetricCreate,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> WorkLifeBalanceMetric:
    """Record weekly work-life balance metrics."""
    service = WorkLifeBalanceService()
    
    try:
        return await service.record_weekly_metrics(db, metric_data=metric_data)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/work-life-balance", response_model=List[WorkLifeBalanceMetric])
async def list_work_life_balance_metrics(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    employee_id: Optional[UUID] = None,
    status: Optional[WorkLifeBalanceStatus] = None,
    weeks: int = Query(12, ge=1, le=52),
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> List[WorkLifeBalanceMetric]:
    """List work-life balance metrics with filtering options."""
    service = WorkLifeBalanceService()
    
    if employee_id:
        return await service.get_employee_balance_trend(db, employee_id, weeks=weeks)
    
    # TODO: Add status filtering and pagination
    return await service.get_multi(db, skip=skip, limit=limit)


@router.get("/work-life-balance/at-risk", response_model=List[WorkLifeBalanceMetric])
async def get_at_risk_employees(
    days: int = Query(30, ge=1, le=365),
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> List[WorkLifeBalanceMetric]:
    """Get employees at risk based on work-life balance metrics."""
    service = WorkLifeBalanceService()
    return await service.get_at_risk_employees(db, days=days)


@router.get("/work-life-balance/{metric_id}", response_model=WorkLifeBalanceMetric)
async def get_work_life_balance_metric(
    metric_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> WorkLifeBalanceMetric:
    """Get specific work-life balance metric."""
    service = WorkLifeBalanceService()
    metric = await service.get(db, metric_id)
    
    if not metric:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Work-life balance metric not found"
        )
    
    return metric


@router.put("/work-life-balance/{metric_id}", response_model=WorkLifeBalanceMetric)
async def update_work_life_balance_metric(
    metric_id: UUID,
    metric_data: WorkLifeBalanceMetricUpdate,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> WorkLifeBalanceMetric:
    """Update work-life balance metric."""
    service = WorkLifeBalanceService()
    
    metric = await service.get(db, metric_id)
    if not metric:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Work-life balance metric not found"
        )
    
    return await service.update(db, db_obj=metric, obj_in=metric_data)


@router.get("/dashboard", response_model=CareerDashboard)
async def get_career_dashboard(
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> CareerDashboard:
    """Get career development dashboard data."""
    service = CareerAnalyticsService()
    return await service.get_career_dashboard(db)


@router.get("/metrics", response_model=CareerMetrics)
async def get_career_metrics(
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> CareerMetrics:
    """Get comprehensive career development metrics."""
    service = CareerAnalyticsService()
    return await service.get_career_metrics(db, start_date=start_date, end_date=end_date)
