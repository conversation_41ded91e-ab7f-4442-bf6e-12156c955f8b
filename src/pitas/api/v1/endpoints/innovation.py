"""Innovation management and technology evaluation API endpoints."""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from pitas.api.deps import get_current_user_id, get_db
from pitas.db.models.innovation import InnovationStatus, TechnologyCategory
from pitas.schemas.innovation import (
    InnovationProject, InnovationProjectCreate, InnovationProjectUpdate,
    InnovationEvaluation, InnovationEvaluationCreate,
    InnovationMilestone, InnovationMilestoneCreate, InnovationMilestoneUpdate,
    TechnologyTrend, TechnologyTrendCreate, TechnologyTrendUpdate,
    ProofOfConcept, ProofOfConceptCreate, ProofOfConceptUpdate,
    InnovationPipeline, InnovationMetrics, InnovationSummary
)
from pitas.services.innovation import InnovationService

router = APIRouter()


@router.post("/projects", response_model=InnovationProject)
async def create_innovation_project(
    project_data: InnovationProjectCreate,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> InnovationProject:
    """Create new innovation project."""
    service = InnovationService(db)
    return await service.create_innovation_project(current_user_id, project_data)


@router.get("/projects", response_model=List[InnovationProject])
async def list_innovation_projects(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: Optional[InnovationStatus] = None,
    category: Optional[TechnologyCategory] = None,
    assigned_to_me: bool = False,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> List[InnovationProject]:
    """List innovation projects with filtering."""
    service = InnovationService(db)
    
    filters = {}
    if status:
        filters["status"] = status
    if category:
        filters["category"] = category
    if assigned_to_me:
        filters["assigned_to"] = current_user_id
    
    return await service.get_multi(skip=skip, limit=limit, **filters)


@router.get("/projects/{project_id}", response_model=InnovationProject)
async def get_innovation_project(
    project_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> InnovationProject:
    """Get specific innovation project."""
    service = InnovationService(db)
    project = await service.get(project_id)
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Innovation project not found"
        )
    
    return project


@router.put("/projects/{project_id}", response_model=InnovationProject)
async def update_innovation_project(
    project_id: UUID,
    project_data: InnovationProjectUpdate,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> InnovationProject:
    """Update innovation project."""
    service = InnovationService(db)
    
    project = await service.get(project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Innovation project not found"
        )
    
    return await service.update(project_id, project_data)


@router.put("/projects/{project_id}/status", response_model=InnovationProject)
async def update_project_status(
    project_id: UUID,
    status: InnovationStatus,
    assigned_to: Optional[UUID] = None,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> InnovationProject:
    """Update innovation project status."""
    service = InnovationService(db)
    return await service.update_project_status(project_id, status, assigned_to)


@router.post("/projects/{project_id}/evaluations", response_model=InnovationEvaluation)
async def evaluate_innovation_project(
    project_id: UUID,
    evaluation_data: InnovationEvaluationCreate,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> InnovationEvaluation:
    """Add evaluation to innovation project."""
    service = InnovationService(db)
    
    # Ensure project_id matches
    evaluation_data.project_id = project_id
    
    return await service.evaluate_project(project_id, current_user_id, evaluation_data)


@router.get("/projects/{project_id}/evaluations", response_model=List[InnovationEvaluation])
async def get_project_evaluations(
    project_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> List[InnovationEvaluation]:
    """Get evaluations for innovation project."""
    service = InnovationService(db)
    
    # This would be implemented in the service
    # For now, return empty list
    return []


@router.post("/milestones", response_model=InnovationMilestone)
async def create_milestone(
    milestone_data: InnovationMilestoneCreate,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> InnovationMilestone:
    """Create innovation project milestone."""
    service = InnovationService(db)
    return await service.create_milestone(milestone_data)


@router.get("/projects/{project_id}/milestones", response_model=List[InnovationMilestone])
async def get_project_milestones(
    project_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> List[InnovationMilestone]:
    """Get milestones for innovation project."""
    service = InnovationService(db)
    
    # This would be implemented in the service
    # For now, return empty list
    return []


@router.put("/milestones/{milestone_id}", response_model=InnovationMilestone)
async def update_milestone(
    milestone_id: UUID,
    milestone_data: InnovationMilestoneUpdate,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> InnovationMilestone:
    """Update innovation project milestone."""
    service = InnovationService(db)
    return await service.update_milestone(milestone_id, milestone_data)


@router.post("/technology-trends", response_model=TechnologyTrend)
async def create_technology_trend(
    trend_data: TechnologyTrendCreate,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> TechnologyTrend:
    """Track new technology trend."""
    service = InnovationService(db)
    return await service.track_technology_trend(trend_data)


@router.get("/technology-trends", response_model=List[TechnologyTrend])
async def list_technology_trends(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    category: Optional[TechnologyCategory] = None,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> List[TechnologyTrend]:
    """List technology trends."""
    service = InnovationService(db)
    return await service.get_trending_technologies(limit, category)


@router.get("/technology-trends/{trend_id}", response_model=TechnologyTrend)
async def get_technology_trend(
    trend_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> TechnologyTrend:
    """Get specific technology trend."""
    service = InnovationService(db)
    
    # This would be implemented in the service
    # For now, raise not found
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="Technology trend not found"
    )


@router.post("/proof-of-concepts", response_model=ProofOfConcept)
async def create_proof_of_concept(
    poc_data: ProofOfConceptCreate,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> ProofOfConcept:
    """Create proof of concept project."""
    service = InnovationService(db)
    return await service.create_proof_of_concept(current_user_id, poc_data)


@router.get("/proof-of-concepts", response_model=List[ProofOfConcept])
async def list_proof_of_concepts(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> List[ProofOfConcept]:
    """List proof of concept projects."""
    service = InnovationService(db)
    
    # This would be implemented in the service
    # For now, return empty list
    return []


@router.get("/proof-of-concepts/{poc_id}", response_model=ProofOfConcept)
async def get_proof_of_concept(
    poc_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> ProofOfConcept:
    """Get specific proof of concept."""
    service = InnovationService(db)
    
    # This would be implemented in the service
    # For now, raise not found
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="Proof of concept not found"
    )


@router.get("/pipeline", response_model=InnovationPipeline)
async def get_innovation_pipeline(
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> InnovationPipeline:
    """Get innovation pipeline overview."""
    service = InnovationService(db)
    return await service.get_innovation_pipeline()


@router.get("/metrics", response_model=InnovationMetrics)
async def get_innovation_metrics(
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> InnovationMetrics:
    """Get innovation metrics and KPIs."""
    # This would be implemented with real metrics calculation
    # For now, return sample data
    return InnovationMetrics(
        feature_adoption_rate=85.0,
        innovation_pipeline_count=12,
        technology_adoption_rate=25.0,
        innovation_roi=2.5,
        proof_of_concept_success_rate=70.0,
        time_to_market_avg_days=120.0,
        innovation_budget_utilization=78.0
    )


@router.get("/summary", response_model=InnovationSummary)
async def get_innovation_summary(
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> InnovationSummary:
    """Get comprehensive innovation summary."""
    service = InnovationService(db)
    
    # Get pipeline overview
    pipeline = await service.get_innovation_pipeline()
    
    # Get metrics (sample data)
    metrics = InnovationMetrics(
        feature_adoption_rate=85.0,
        innovation_pipeline_count=12,
        technology_adoption_rate=25.0,
        innovation_roi=2.5,
        proof_of_concept_success_rate=70.0,
        time_to_market_avg_days=120.0,
        innovation_budget_utilization=78.0
    )
    
    # Get recent projects
    recent_projects = await service.get_multi(limit=5)
    
    # Get trending technologies
    trending_technologies = await service.get_trending_technologies(limit=5)
    
    # Get upcoming evaluations (projects in evaluation status)
    upcoming_evaluations = await service.get_multi(
        limit=5,
        status=InnovationStatus.EVALUATION
    )
    
    return InnovationSummary(
        pipeline=pipeline,
        metrics=metrics,
        recent_projects=recent_projects,
        trending_technologies=trending_technologies,
        upcoming_evaluations=upcoming_evaluations
    )


@router.get("/my-projects", response_model=List[InnovationProject])
async def get_my_innovation_projects(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user_id: UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
) -> List[InnovationProject]:
    """Get current user's innovation projects."""
    service = InnovationService(db)
    return await service.get_multi(
        skip=skip,
        limit=limit,
        proposed_by=current_user_id
    )
