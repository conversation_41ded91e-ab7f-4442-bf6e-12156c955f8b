"""Innovation management and technology evaluation schemas."""

from datetime import datetime
from typing import Optional, Dict, List, Any
from uuid import UUID

from pydantic import BaseModel, Field, ConfigDict

from pitas.db.models.innovation import (
    InnovationStatus, TechnologyCategory, PriorityLevel
)


class InnovationProjectBase(BaseModel):
    """Base schema for innovation projects."""
    title: str = Field(..., min_length=1, max_length=200)
    description: str = Field(..., min_length=1)
    category: TechnologyCategory
    priority: PriorityLevel
    business_value: str = Field(..., min_length=1)
    expected_roi: Optional[float] = Field(None, ge=0)
    estimated_cost: Optional[float] = Field(None, ge=0)
    estimated_timeline_weeks: Optional[int] = Field(None, ge=1)
    technical_requirements: Optional[str] = None
    dependencies: Optional[List[str]] = None
    risks: Optional[str] = None
    success_criteria: Optional[str] = None
    team_members: Optional[List[UUID]] = None
    total_milestones: Optional[int] = Field(None, ge=1)


class InnovationProjectCreate(InnovationProjectBase):
    """Schema for creating innovation projects."""
    pass


class InnovationProjectUpdate(BaseModel):
    """Schema for updating innovation projects."""
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, min_length=1)
    category: Optional[TechnologyCategory] = None
    priority: Optional[PriorityLevel] = None
    status: Optional[InnovationStatus] = None
    assigned_to: Optional[UUID] = None
    business_value: Optional[str] = Field(None, min_length=1)
    expected_roi: Optional[float] = Field(None, ge=0)
    estimated_cost: Optional[float] = Field(None, ge=0)
    estimated_timeline_weeks: Optional[int] = Field(None, ge=1)
    technical_requirements: Optional[str] = None
    dependencies: Optional[List[str]] = None
    risks: Optional[str] = None
    success_criteria: Optional[str] = None
    team_members: Optional[List[UUID]] = None
    progress_percentage: Optional[float] = Field(None, ge=0, le=100)
    total_milestones: Optional[int] = Field(None, ge=1)


class InnovationProject(InnovationProjectBase):
    """Schema for innovation project response."""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    proposed_by: UUID
    assigned_to: Optional[UUID] = None
    status: InnovationStatus
    feasibility_score: Optional[float] = None
    impact_score: Optional[float] = None
    effort_score: Optional[float] = None
    risk_score: Optional[float] = None
    overall_score: Optional[float] = None
    progress_percentage: float
    milestones_completed: int
    created_at: datetime
    updated_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


class InnovationEvaluationBase(BaseModel):
    """Base schema for innovation evaluations."""
    technical_feasibility: float = Field(..., ge=1, le=10)
    business_impact: float = Field(..., ge=1, le=10)
    implementation_effort: float = Field(..., ge=1, le=10)
    risk_assessment: float = Field(..., ge=1, le=10)
    strategic_alignment: float = Field(..., ge=1, le=10)
    strengths: Optional[str] = None
    weaknesses: Optional[str] = None
    recommendations: Optional[str] = None
    additional_notes: Optional[str] = None
    recommendation: str = Field(..., regex="^(approve|reject|defer)$")


class InnovationEvaluationCreate(InnovationEvaluationBase):
    """Schema for creating innovation evaluations."""
    project_id: UUID


class InnovationEvaluation(InnovationEvaluationBase):
    """Schema for innovation evaluation response."""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    project_id: UUID
    evaluator_id: UUID
    created_at: datetime


class InnovationMilestoneBase(BaseModel):
    """Base schema for innovation milestones."""
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    deliverables: Optional[List[str]] = None
    planned_start_date: Optional[datetime] = None
    planned_end_date: Optional[datetime] = None
    depends_on: Optional[List[UUID]] = None


class InnovationMilestoneCreate(InnovationMilestoneBase):
    """Schema for creating innovation milestones."""
    project_id: UUID


class InnovationMilestoneUpdate(BaseModel):
    """Schema for updating innovation milestones."""
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    deliverables: Optional[List[str]] = None
    planned_start_date: Optional[datetime] = None
    planned_end_date: Optional[datetime] = None
    actual_start_date: Optional[datetime] = None
    actual_end_date: Optional[datetime] = None
    is_completed: Optional[bool] = None
    completion_percentage: Optional[float] = Field(None, ge=0, le=100)
    depends_on: Optional[List[UUID]] = None


class InnovationMilestone(InnovationMilestoneBase):
    """Schema for innovation milestone response."""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    project_id: UUID
    actual_start_date: Optional[datetime] = None
    actual_end_date: Optional[datetime] = None
    is_completed: bool
    completion_percentage: float
    created_at: datetime
    updated_at: datetime


class TechnologyTrendBase(BaseModel):
    """Base schema for technology trends."""
    name: str = Field(..., min_length=1, max_length=200)
    description: str = Field(..., min_length=1)
    category: TechnologyCategory
    maturity_level: str = Field(..., max_length=50)
    adoption_rate: Optional[float] = Field(None, ge=0, le=100)
    market_impact: Optional[float] = Field(None, ge=1, le=10)
    relevance_score: Optional[float] = Field(None, ge=1, le=10)
    source_urls: Optional[List[str]] = None
    research_papers: Optional[List[str]] = None
    vendor_information: Optional[Dict[str, Any]] = None
    opportunities: Optional[str] = None
    threats: Optional[str] = None
    implementation_considerations: Optional[str] = None


class TechnologyTrendCreate(TechnologyTrendBase):
    """Schema for creating technology trends."""
    first_identified: datetime


class TechnologyTrendUpdate(BaseModel):
    """Schema for updating technology trends."""
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, min_length=1)
    category: Optional[TechnologyCategory] = None
    maturity_level: Optional[str] = Field(None, max_length=50)
    adoption_rate: Optional[float] = Field(None, ge=0, le=100)
    market_impact: Optional[float] = Field(None, ge=1, le=10)
    relevance_score: Optional[float] = Field(None, ge=1, le=10)
    source_urls: Optional[List[str]] = None
    research_papers: Optional[List[str]] = None
    vendor_information: Optional[Dict[str, Any]] = None
    opportunities: Optional[str] = None
    threats: Optional[str] = None
    implementation_considerations: Optional[str] = None


class TechnologyTrend(TechnologyTrendBase):
    """Schema for technology trend response."""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    first_identified: datetime
    last_updated: datetime


class ProofOfConceptBase(BaseModel):
    """Base schema for proof of concepts."""
    title: str = Field(..., min_length=1, max_length=200)
    objective: str = Field(..., min_length=1)
    hypothesis: str = Field(..., min_length=1)
    methodology: str = Field(..., min_length=1)
    tools_used: Optional[List[str]] = None
    resources_required: Optional[Dict[str, Any]] = None
    start_date: datetime
    end_date: Optional[datetime] = None
    team_members: Optional[List[UUID]] = None


class ProofOfConceptCreate(ProofOfConceptBase):
    """Schema for creating proof of concepts."""
    innovation_project_id: Optional[UUID] = None


class ProofOfConceptUpdate(BaseModel):
    """Schema for updating proof of concepts."""
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    objective: Optional[str] = Field(None, min_length=1)
    hypothesis: Optional[str] = Field(None, min_length=1)
    methodology: Optional[str] = Field(None, min_length=1)
    tools_used: Optional[List[str]] = None
    resources_required: Optional[Dict[str, Any]] = None
    results_summary: Optional[str] = None
    success_metrics: Optional[Dict[str, Any]] = None
    lessons_learned: Optional[str] = None
    recommendations: Optional[str] = None
    status: Optional[str] = None
    is_successful: Optional[bool] = None
    end_date: Optional[datetime] = None
    duration_days: Optional[int] = Field(None, ge=1)
    team_members: Optional[List[UUID]] = None


class ProofOfConcept(ProofOfConceptBase):
    """Schema for proof of concept response."""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    innovation_project_id: Optional[UUID] = None
    lead_researcher: UUID
    results_summary: Optional[str] = None
    success_metrics: Optional[Dict[str, Any]] = None
    lessons_learned: Optional[str] = None
    recommendations: Optional[str] = None
    status: str
    is_successful: Optional[bool] = None
    duration_days: Optional[int] = None
    created_at: datetime


class InnovationPipeline(BaseModel):
    """Schema for innovation pipeline overview."""
    total_projects: int
    projects_by_status: Dict[str, int]
    projects_by_category: Dict[str, int]
    projects_by_priority: Dict[str, int]
    average_completion_time: Optional[float]
    success_rate: Optional[float]
    total_investment: Optional[float]
    projected_roi: Optional[float]


class InnovationMetrics(BaseModel):
    """Schema for innovation metrics and KPIs."""
    feature_adoption_rate: float
    innovation_pipeline_count: int
    technology_adoption_rate: float
    innovation_roi: float
    proof_of_concept_success_rate: float
    time_to_market_avg_days: Optional[float]
    innovation_budget_utilization: float


class InnovationSummary(BaseModel):
    """Schema for comprehensive innovation summary."""
    pipeline: InnovationPipeline
    metrics: InnovationMetrics
    recent_projects: List[InnovationProject]
    trending_technologies: List[TechnologyTrend]
    upcoming_evaluations: List[InnovationProject]
