"""Feedback and user experience analytics schemas."""

from datetime import datetime
from typing import Optional, Dict, List, Any
from uuid import UUID

from pydantic import BaseModel, Field, ConfigDict

from pitas.db.models.feedback import FeedbackType, FeedbackStatus, SentimentScore


class UserFeedbackBase(BaseModel):
    """Base schema for user feedback."""
    feedback_type: FeedbackType
    title: str = Field(..., min_length=1, max_length=200)
    description: str = Field(..., min_length=1)
    rating: Optional[int] = Field(None, ge=1, le=5)
    nps_score: Optional[int] = Field(None, ge=0, le=10)
    feature_area: Optional[str] = Field(None, max_length=100)
    workflow_step: Optional[str] = Field(None, max_length=100)
    page_url: Optional[str] = Field(None, max_length=500)
    user_agent: Optional[str] = Field(None, max_length=500)
    session_id: Optional[str] = Field(None, max_length=100)


class UserFeedbackCreate(UserFeedbackBase):
    """Schema for creating user feedback."""
    pass


class UserFeedbackUpdate(BaseModel):
    """Schema for updating user feedback."""
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, min_length=1)
    rating: Optional[int] = Field(None, ge=1, le=5)
    status: Optional[FeedbackStatus] = None
    assigned_to: Optional[UUID] = None
    resolution_notes: Optional[str] = None


class UserFeedback(UserFeedbackBase):
    """Schema for user feedback response."""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    user_id: UUID
    sentiment_score: Optional[SentimentScore] = None
    priority_score: Optional[float] = None
    category_tags: Optional[Dict[str, Any]] = None
    status: FeedbackStatus
    assigned_to: Optional[UUID] = None
    resolution_notes: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    resolved_at: Optional[datetime] = None


class FeedbackResponseBase(BaseModel):
    """Base schema for feedback responses."""
    response_text: str = Field(..., min_length=1)
    is_public: bool = True


class FeedbackResponseCreate(FeedbackResponseBase):
    """Schema for creating feedback responses."""
    feedback_id: UUID


class FeedbackResponse(FeedbackResponseBase):
    """Schema for feedback response."""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    feedback_id: UUID
    responder_id: UUID
    created_at: datetime


class UserBehaviorAnalyticsBase(BaseModel):
    """Base schema for user behavior analytics."""
    session_id: str = Field(..., max_length=100)
    event_type: str = Field(..., max_length=100)
    event_data: Dict[str, Any]
    page_url: str = Field(..., max_length=500)
    referrer_url: Optional[str] = Field(None, max_length=500)
    page_load_time: Optional[float] = Field(None, ge=0)
    time_on_page: Optional[float] = Field(None, ge=0)
    click_count: Optional[int] = Field(None, ge=0)
    scroll_depth: Optional[float] = Field(None, ge=0, le=100)
    user_agent: str = Field(..., max_length=500)
    screen_resolution: Optional[str] = Field(None, max_length=20)
    viewport_size: Optional[str] = Field(None, max_length=20)


class UserBehaviorAnalyticsCreate(UserBehaviorAnalyticsBase):
    """Schema for creating user behavior analytics."""
    pass


class UserBehaviorAnalytics(UserBehaviorAnalyticsBase):
    """Schema for user behavior analytics response."""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    user_id: UUID
    timestamp: datetime


class FeatureUsageMetricsBase(BaseModel):
    """Base schema for feature usage metrics."""
    feature_name: str = Field(..., max_length=100)
    usage_count: int = Field(1, ge=1)
    total_time_spent: float = Field(0.0, ge=0)
    success_rate: Optional[float] = Field(None, ge=0, le=100)
    error_count: int = Field(0, ge=0)
    usage_context: Optional[Dict[str, Any]] = None


class FeatureUsageMetricsCreate(FeatureUsageMetricsBase):
    """Schema for creating feature usage metrics."""
    pass


class FeatureUsageMetricsUpdate(BaseModel):
    """Schema for updating feature usage metrics."""
    usage_count: Optional[int] = Field(None, ge=1)
    total_time_spent: Optional[float] = Field(None, ge=0)
    success_rate: Optional[float] = Field(None, ge=0, le=100)
    error_count: Optional[int] = Field(None, ge=0)
    usage_context: Optional[Dict[str, Any]] = None


class FeatureUsageMetrics(FeatureUsageMetricsBase):
    """Schema for feature usage metrics response."""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    user_id: UUID
    first_used: datetime
    last_used: datetime


class SatisfactionSurveyBase(BaseModel):
    """Base schema for satisfaction surveys."""
    survey_type: str = Field(..., max_length=50)
    nps_score: Optional[int] = Field(None, ge=0, le=10)
    satisfaction_rating: Optional[int] = Field(None, ge=1, le=5)
    likelihood_to_recommend: Optional[int] = Field(None, ge=1, le=5)
    what_works_well: Optional[str] = None
    improvement_suggestions: Optional[str] = None
    additional_comments: Optional[str] = None
    survey_version: str = Field("1.0", max_length=20)
    completion_time_seconds: Optional[int] = Field(None, ge=0)


class SatisfactionSurveyCreate(SatisfactionSurveyBase):
    """Schema for creating satisfaction surveys."""
    pass


class SatisfactionSurvey(SatisfactionSurveyBase):
    """Schema for satisfaction survey response."""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    user_id: UUID
    created_at: datetime


class FeedbackAnalytics(BaseModel):
    """Schema for feedback analytics summary."""
    total_feedback: int
    feedback_by_type: Dict[str, int]
    feedback_by_status: Dict[str, int]
    average_rating: Optional[float]
    average_nps_score: Optional[float]
    sentiment_distribution: Dict[str, int]
    resolution_time_avg_hours: Optional[float]
    top_feature_areas: List[Dict[str, Any]]
    trending_issues: List[Dict[str, Any]]


class UserEngagementMetrics(BaseModel):
    """Schema for user engagement metrics."""
    total_users: int
    active_users_last_30_days: int
    average_session_duration: float
    page_views_per_session: float
    bounce_rate: float
    feature_adoption_rates: Dict[str, float]
    user_journey_analytics: Dict[str, Any]


class FeedbackTrends(BaseModel):
    """Schema for feedback trends over time."""
    period: str  # daily, weekly, monthly
    feedback_volume: List[Dict[str, Any]]
    satisfaction_trends: List[Dict[str, Any]]
    nps_trends: List[Dict[str, Any]]
    resolution_time_trends: List[Dict[str, Any]]


class FeedbackSummary(BaseModel):
    """Schema for comprehensive feedback summary."""
    analytics: FeedbackAnalytics
    engagement_metrics: UserEngagementMetrics
    trends: FeedbackTrends
    recent_feedback: List[UserFeedback]
    improvement_opportunities: List[str]
