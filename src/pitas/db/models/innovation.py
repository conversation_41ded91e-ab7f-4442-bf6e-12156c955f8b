"""Innovation management and technology evaluation models."""

from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import UUID, uuid4

from sqlalchemy import (
    <PERSON><PERSON><PERSON>, DateTime, Float, <PERSON>Key, Integer, String, Text, JSON
)
from sqlalchemy.dialects.postgresql import UUID as PostgreSQLUUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from pitas.db.base import Base


class InnovationStatus(str, Enum):
    """Status of innovation projects."""
    IDEA = "idea"
    EVALUATION = "evaluation"
    PROOF_OF_CONCEPT = "proof_of_concept"
    PILOT = "pilot"
    IMPLEMENTATION = "implementation"
    DEPLOYED = "deployed"
    REJECTED = "rejected"
    DEFERRED = "deferred"


class TechnologyCategory(str, Enum):
    """Categories of technology innovations."""
    ARTIFICIAL_INTELLIGENCE = "artificial_intelligence"
    MACHINE_LEARNING = "machine_learning"
    AUTOMATION = "automation"
    SECURITY_TOOLS = "security_tools"
    CLOUD_SERVICES = "cloud_services"
    ANALYTICS = "analytics"
    INTEGRATION = "integration"
    USER_EXPERIENCE = "user_experience"
    PERFORMANCE = "performance"
    COMPLIANCE = "compliance"


class PriorityLevel(str, Enum):
    """Priority levels for innovations."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class InnovationProject(Base):
    """Innovation project tracking and management."""
    
    __tablename__ = "innovation_projects"
    
    id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        primary_key=True,
        default=uuid4
    )
    title: Mapped[str] = mapped_column(String(200), nullable=False)
    description: Mapped[str] = mapped_column(Text, nullable=False)
    
    # Classification
    category: Mapped[TechnologyCategory] = mapped_column(String(50), nullable=False)
    priority: Mapped[PriorityLevel] = mapped_column(String(20), nullable=False)
    status: Mapped[InnovationStatus] = mapped_column(
        String(30),
        default=InnovationStatus.IDEA
    )
    
    # Project details
    proposed_by: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False
    )
    assigned_to: Mapped[Optional[UUID]] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("users.id")
    )
    team_members: Mapped[Optional[list]] = mapped_column(JSON)
    
    # Business case
    business_value: Mapped[str] = mapped_column(Text, nullable=False)
    expected_roi: Mapped[Optional[float]] = mapped_column(Float)
    estimated_cost: Mapped[Optional[float]] = mapped_column(Float)
    estimated_timeline_weeks: Mapped[Optional[int]] = mapped_column(Integer)
    
    # Technical details
    technical_requirements: Mapped[Optional[str]] = mapped_column(Text)
    dependencies: Mapped[Optional[list]] = mapped_column(JSON)
    risks: Mapped[Optional[str]] = mapped_column(Text)
    success_criteria: Mapped[Optional[str]] = mapped_column(Text)
    
    # Evaluation scores
    feasibility_score: Mapped[Optional[float]] = mapped_column(Float)  # 1-10
    impact_score: Mapped[Optional[float]] = mapped_column(Float)  # 1-10
    effort_score: Mapped[Optional[float]] = mapped_column(Float)  # 1-10
    risk_score: Mapped[Optional[float]] = mapped_column(Float)  # 1-10
    overall_score: Mapped[Optional[float]] = mapped_column(Float)  # Calculated
    
    # Progress tracking
    progress_percentage: Mapped[float] = mapped_column(Float, default=0.0)
    milestones_completed: Mapped[int] = mapped_column(Integer, default=0)
    total_milestones: Mapped[Optional[int]] = mapped_column(Integer)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime,
        default=datetime.utcnow
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime,
        default=datetime.utcnow,
        onupdate=datetime.utcnow
    )
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    
    # Relationships
    proposer = relationship("User", foreign_keys=[proposed_by])
    assignee = relationship("User", foreign_keys=[assigned_to])
    evaluations = relationship("InnovationEvaluation", back_populates="project")
    milestones = relationship("InnovationMilestone", back_populates="project")


class InnovationEvaluation(Base):
    """Evaluation records for innovation projects."""
    
    __tablename__ = "innovation_evaluations"
    
    id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        primary_key=True,
        default=uuid4
    )
    project_id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("innovation_projects.id"),
        nullable=False
    )
    evaluator_id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False
    )
    
    # Evaluation criteria
    technical_feasibility: Mapped[float] = mapped_column(Float, nullable=False)  # 1-10
    business_impact: Mapped[float] = mapped_column(Float, nullable=False)  # 1-10
    implementation_effort: Mapped[float] = mapped_column(Float, nullable=False)  # 1-10
    risk_assessment: Mapped[float] = mapped_column(Float, nullable=False)  # 1-10
    strategic_alignment: Mapped[float] = mapped_column(Float, nullable=False)  # 1-10
    
    # Detailed feedback
    strengths: Mapped[Optional[str]] = mapped_column(Text)
    weaknesses: Mapped[Optional[str]] = mapped_column(Text)
    recommendations: Mapped[Optional[str]] = mapped_column(Text)
    additional_notes: Mapped[Optional[str]] = mapped_column(Text)
    
    # Decision
    recommendation: Mapped[str] = mapped_column(String(20), nullable=False)  # approve/reject/defer
    
    created_at: Mapped[datetime] = mapped_column(
        DateTime,
        default=datetime.utcnow
    )
    
    # Relationships
    project = relationship("InnovationProject", back_populates="evaluations")
    evaluator = relationship("User")


class InnovationMilestone(Base):
    """Milestones for innovation projects."""
    
    __tablename__ = "innovation_milestones"
    
    id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        primary_key=True,
        default=uuid4
    )
    project_id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("innovation_projects.id"),
        nullable=False
    )
    
    title: Mapped[str] = mapped_column(String(200), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    deliverables: Mapped[Optional[list]] = mapped_column(JSON)
    
    # Timeline
    planned_start_date: Mapped[Optional[datetime]] = mapped_column(DateTime)
    planned_end_date: Mapped[Optional[datetime]] = mapped_column(DateTime)
    actual_start_date: Mapped[Optional[datetime]] = mapped_column(DateTime)
    actual_end_date: Mapped[Optional[datetime]] = mapped_column(DateTime)
    
    # Status
    is_completed: Mapped[bool] = mapped_column(Boolean, default=False)
    completion_percentage: Mapped[float] = mapped_column(Float, default=0.0)
    
    # Dependencies
    depends_on: Mapped[Optional[list]] = mapped_column(JSON)  # List of milestone IDs
    
    created_at: Mapped[datetime] = mapped_column(
        DateTime,
        default=datetime.utcnow
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime,
        default=datetime.utcnow,
        onupdate=datetime.utcnow
    )
    
    # Relationships
    project = relationship("InnovationProject", back_populates="milestones")


class TechnologyTrend(Base):
    """Tracking of emerging technology trends."""
    
    __tablename__ = "technology_trends"
    
    id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        primary_key=True,
        default=uuid4
    )
    
    name: Mapped[str] = mapped_column(String(200), nullable=False)
    description: Mapped[str] = mapped_column(Text, nullable=False)
    category: Mapped[TechnologyCategory] = mapped_column(String(50), nullable=False)
    
    # Trend analysis
    maturity_level: Mapped[str] = mapped_column(String(50), nullable=False)  # emerging/growing/mature/declining
    adoption_rate: Mapped[Optional[float]] = mapped_column(Float)  # 0-100%
    market_impact: Mapped[Optional[float]] = mapped_column(Float)  # 1-10
    relevance_score: Mapped[Optional[float]] = mapped_column(Float)  # 1-10
    
    # Sources and references
    source_urls: Mapped[Optional[list]] = mapped_column(JSON)
    research_papers: Mapped[Optional[list]] = mapped_column(JSON)
    vendor_information: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Tracking
    first_identified: Mapped[datetime] = mapped_column(DateTime, nullable=False)
    last_updated: Mapped[datetime] = mapped_column(
        DateTime,
        default=datetime.utcnow,
        onupdate=datetime.utcnow
    )
    
    # Analysis notes
    opportunities: Mapped[Optional[str]] = mapped_column(Text)
    threats: Mapped[Optional[str]] = mapped_column(Text)
    implementation_considerations: Mapped[Optional[str]] = mapped_column(Text)


class ProofOfConcept(Base):
    """Proof of concept projects and results."""
    
    __tablename__ = "proof_of_concepts"
    
    id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        primary_key=True,
        default=uuid4
    )
    innovation_project_id: Mapped[Optional[UUID]] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("innovation_projects.id")
    )
    
    title: Mapped[str] = mapped_column(String(200), nullable=False)
    objective: Mapped[str] = mapped_column(Text, nullable=False)
    hypothesis: Mapped[str] = mapped_column(Text, nullable=False)
    
    # Implementation details
    methodology: Mapped[str] = mapped_column(Text, nullable=False)
    tools_used: Mapped[Optional[list]] = mapped_column(JSON)
    resources_required: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Results
    results_summary: Mapped[Optional[str]] = mapped_column(Text)
    success_metrics: Mapped[Optional[dict]] = mapped_column(JSON)
    lessons_learned: Mapped[Optional[str]] = mapped_column(Text)
    recommendations: Mapped[Optional[str]] = mapped_column(Text)
    
    # Status
    status: Mapped[str] = mapped_column(String(20), default="planning")
    is_successful: Mapped[Optional[bool]] = mapped_column(Boolean)
    
    # Timeline
    start_date: Mapped[datetime] = mapped_column(DateTime, nullable=False)
    end_date: Mapped[Optional[datetime]] = mapped_column(DateTime)
    duration_days: Mapped[Optional[int]] = mapped_column(Integer)
    
    # Team
    lead_researcher: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False
    )
    team_members: Mapped[Optional[list]] = mapped_column(JSON)
    
    created_at: Mapped[datetime] = mapped_column(
        DateTime,
        default=datetime.utcnow
    )
    
    # Relationships
    innovation_project = relationship("InnovationProject")
    lead = relationship("User")
