"""Feedback and user experience analytics models."""

from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import UUID, uuid4

from sqlalchemy import (
    <PERSON><PERSON><PERSON>, DateTime, Float, Foreign<PERSON>ey, Integer, String, Text, JSON
)
from sqlalchemy.dialects.postgresql import UUID as PostgreSQLUUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from pitas.db.base import Base


class FeedbackType(str, Enum):
    """Types of feedback that can be collected."""
    USER_EXPERIENCE = "user_experience"
    FEATURE_REQUEST = "feature_request"
    BUG_REPORT = "bug_report"
    PERFORMANCE_ISSUE = "performance_issue"
    WORKFLOW_IMPROVEMENT = "workflow_improvement"
    GENERAL_SATISFACTION = "general_satisfaction"
    NPS_SURVEY = "nps_survey"


class FeedbackStatus(str, Enum):
    """Status of feedback items."""
    SUBMITTED = "submitted"
    UNDER_REVIEW = "under_review"
    IN_PROGRESS = "in_progress"
    IMPLEMENTED = "implemented"
    REJECTED = "rejected"
    DEFERRED = "deferred"


class SentimentScore(str, Enum):
    """Sentiment analysis scores."""
    VERY_POSITIVE = "very_positive"
    POSITIVE = "positive"
    NEUTRAL = "neutral"
    NEGATIVE = "negative"
    VERY_NEGATIVE = "very_negative"


class UserFeedback(Base):
    """User feedback collection model."""
    
    __tablename__ = "user_feedback"
    
    id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        primary_key=True,
        default=uuid4
    )
    user_id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False
    )
    feedback_type: Mapped[FeedbackType] = mapped_column(
        String(50),
        nullable=False
    )
    title: Mapped[str] = mapped_column(String(200), nullable=False)
    description: Mapped[str] = mapped_column(Text, nullable=False)
    rating: Mapped[Optional[int]] = mapped_column(Integer)  # 1-5 scale
    nps_score: Mapped[Optional[int]] = mapped_column(Integer)  # 0-10 scale
    
    # Context information
    feature_area: Mapped[Optional[str]] = mapped_column(String(100))
    workflow_step: Mapped[Optional[str]] = mapped_column(String(100))
    page_url: Mapped[Optional[str]] = mapped_column(String(500))
    user_agent: Mapped[Optional[str]] = mapped_column(String(500))
    session_id: Mapped[Optional[str]] = mapped_column(String(100))
    
    # Analysis results
    sentiment_score: Mapped[Optional[SentimentScore]] = mapped_column(String(20))
    priority_score: Mapped[Optional[float]] = mapped_column(Float)
    category_tags: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Status tracking
    status: Mapped[FeedbackStatus] = mapped_column(
        String(20),
        default=FeedbackStatus.SUBMITTED
    )
    assigned_to: Mapped[Optional[UUID]] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("users.id")
    )
    resolution_notes: Mapped[Optional[str]] = mapped_column(Text)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime,
        default=datetime.utcnow
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime,
        default=datetime.utcnow,
        onupdate=datetime.utcnow
    )
    resolved_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    
    # Relationships
    user = relationship("User", foreign_keys=[user_id], back_populates="feedback_given")
    assigned_user = relationship("User", foreign_keys=[assigned_to])
    responses = relationship("FeedbackResponse", back_populates="feedback")


class FeedbackResponse(Base):
    """Responses to user feedback."""
    
    __tablename__ = "feedback_responses"
    
    id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        primary_key=True,
        default=uuid4
    )
    feedback_id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("user_feedback.id"),
        nullable=False
    )
    responder_id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False
    )
    response_text: Mapped[str] = mapped_column(Text, nullable=False)
    is_public: Mapped[bool] = mapped_column(Boolean, default=True)
    
    created_at: Mapped[datetime] = mapped_column(
        DateTime,
        default=datetime.utcnow
    )
    
    # Relationships
    feedback = relationship("UserFeedback", back_populates="responses")
    responder = relationship("User")


class UserBehaviorAnalytics(Base):
    """User behavior tracking for analytics."""
    
    __tablename__ = "user_behavior_analytics"
    
    id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        primary_key=True,
        default=uuid4
    )
    user_id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False
    )
    session_id: Mapped[str] = mapped_column(String(100), nullable=False)
    
    # Event tracking
    event_type: Mapped[str] = mapped_column(String(100), nullable=False)
    event_data: Mapped[dict] = mapped_column(JSON)
    page_url: Mapped[str] = mapped_column(String(500), nullable=False)
    referrer_url: Mapped[Optional[str]] = mapped_column(String(500))
    
    # Performance metrics
    page_load_time: Mapped[Optional[float]] = mapped_column(Float)
    time_on_page: Mapped[Optional[float]] = mapped_column(Float)
    click_count: Mapped[Optional[int]] = mapped_column(Integer)
    scroll_depth: Mapped[Optional[float]] = mapped_column(Float)
    
    # Device and browser info
    user_agent: Mapped[str] = mapped_column(String(500), nullable=False)
    screen_resolution: Mapped[Optional[str]] = mapped_column(String(20))
    viewport_size: Mapped[Optional[str]] = mapped_column(String(20))
    
    timestamp: Mapped[datetime] = mapped_column(
        DateTime,
        default=datetime.utcnow
    )
    
    # Relationships
    user = relationship("User")


class FeatureUsageMetrics(Base):
    """Feature usage tracking and analytics."""
    
    __tablename__ = "feature_usage_metrics"
    
    id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        primary_key=True,
        default=uuid4
    )
    feature_name: Mapped[str] = mapped_column(String(100), nullable=False)
    user_id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False
    )
    
    # Usage metrics
    usage_count: Mapped[int] = mapped_column(Integer, default=1)
    total_time_spent: Mapped[float] = mapped_column(Float, default=0.0)
    success_rate: Mapped[Optional[float]] = mapped_column(Float)
    error_count: Mapped[int] = mapped_column(Integer, default=0)
    
    # Context
    usage_context: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Timestamps
    first_used: Mapped[datetime] = mapped_column(
        DateTime,
        default=datetime.utcnow
    )
    last_used: Mapped[datetime] = mapped_column(
        DateTime,
        default=datetime.utcnow
    )
    
    # Relationships
    user = relationship("User")


class SatisfactionSurvey(Base):
    """Satisfaction surveys and NPS tracking."""
    
    __tablename__ = "satisfaction_surveys"
    
    id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        primary_key=True,
        default=uuid4
    )
    user_id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False
    )
    
    # Survey data
    survey_type: Mapped[str] = mapped_column(String(50), nullable=False)
    nps_score: Mapped[Optional[int]] = mapped_column(Integer)  # 0-10
    satisfaction_rating: Mapped[Optional[int]] = mapped_column(Integer)  # 1-5
    likelihood_to_recommend: Mapped[Optional[int]] = mapped_column(Integer)  # 1-5
    
    # Detailed feedback
    what_works_well: Mapped[Optional[str]] = mapped_column(Text)
    improvement_suggestions: Mapped[Optional[str]] = mapped_column(Text)
    additional_comments: Mapped[Optional[str]] = mapped_column(Text)
    
    # Survey metadata
    survey_version: Mapped[str] = mapped_column(String(20), default="1.0")
    completion_time_seconds: Mapped[Optional[int]] = mapped_column(Integer)
    
    created_at: Mapped[datetime] = mapped_column(
        DateTime,
        default=datetime.utcnow
    )
    
    # Relationships
    user = relationship("User")
