"""Integration tests for service URL manager and Traefik services.

This module contains comprehensive tests to verify that all services are running
correctly and accessible through their *.pitas.localhost URLs.
"""

import asyncio
import pytest
import httpx
from typing import Dict, List
import structlog

from pitas.core.service_url_manager import service_url_manager, ServiceRegistration
from pitas.core.config import settings


logger = structlog.get_logger(__name__)


class TestServiceIntegration:
    """Test suite for service integration and accessibility."""

    @pytest.fixture(autouse=True)
    async def setup_and_teardown(self):
        """Set up and tear down for each test."""
        # Setup
        yield
        # Teardown - clean up any test services
        try:
            await service_url_manager.deregister_service("test-service")
        except:
            pass

    @pytest.mark.asyncio
    async def test_service_url_manager_registration(self):
        """Test service registration and deregistration."""
        # Test service registration
        test_service = ServiceRegistration(
            name="test-service",
            internal_port=9999,
            health_endpoint="/test-health",
            tags=["test", "integration"],
            metadata={"test": "true"}
        )
        
        service_url = await service_url_manager.register_service(test_service)
        assert service_url == "http://test-service.pitas.localhost"
        
        # Test service retrieval
        retrieved_url = service_url_manager.get_service_url("test-service")
        assert retrieved_url == service_url
        
        # Test service info
        service_info = service_url_manager.get_service_info("test-service")
        assert service_info is not None
        assert service_info.name == "test-service"
        assert service_info.internal_port == 9999
        assert "test" in service_info.tags
        
        # Test service listing
        services = service_url_manager.list_services()
        test_service_found = any(s["name"] == "test-service" for s in services)
        assert test_service_found
        
        # Test service deregistration
        success = await service_url_manager.deregister_service("test-service")
        assert success
        
        # Verify service is removed
        retrieved_url = service_url_manager.get_service_url("test-service")
        assert retrieved_url is None

    @pytest.mark.asyncio
    async def test_duplicate_service_registration(self):
        """Test that duplicate service registration raises an error."""
        test_service = ServiceRegistration(
            name="duplicate-test",
            internal_port=9998
        )
        
        # First registration should succeed
        await service_url_manager.register_service(test_service)
        
        # Second registration should fail
        with pytest.raises(ValueError, match="already registered"):
            await service_url_manager.register_service(test_service)
        
        # Clean up
        await service_url_manager.deregister_service("duplicate-test")

    @pytest.mark.asyncio
    async def test_nonexistent_service_deregistration(self):
        """Test deregistration of non-existent service."""
        success = await service_url_manager.deregister_service("nonexistent-service")
        assert not success


class TestTraefikServiceAccessibility:
    """Test suite for Traefik service accessibility."""

    @pytest.fixture
    def service_urls(self) -> Dict[str, str]:
        """Service URLs for testing."""
        base_port = "8081"  # Updated port from docker-compose
        return {
            "traefik_dashboard": f"http://localhost:8082/api/rawdata",  # Direct Traefik API
            "api_health": f"http://api.pitas.localhost:{base_port}/health",
            "api_docs": f"http://api.pitas.localhost:{base_port}/api/v1/docs",
            "grafana": f"http://grafana.pitas.localhost:{base_port}",
            "prometheus": f"http://prometheus.pitas.localhost:{base_port}",
            "neo4j": f"http://neo4j.pitas.localhost:{base_port}",
            "influxdb": f"http://influxdb.pitas.localhost:{base_port}/health"
        }

    @pytest.mark.asyncio
    async def test_traefik_dashboard_accessible(self, service_urls):
        """Test that Traefik dashboard is accessible."""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.get(service_urls["traefik_dashboard"])
                assert response.status_code == 200
                logger.info("Traefik dashboard is accessible")
            except httpx.RequestError as e:
                pytest.skip(f"Traefik dashboard not accessible: {e}")

    @pytest.mark.asyncio
    async def test_api_health_endpoint(self, service_urls):
        """Test that the main API health endpoint is accessible."""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.get(service_urls["api_health"])
                assert response.status_code == 200
                
                # Check response content
                health_data = response.json()
                assert "status" in health_data
                logger.info("API health endpoint is accessible", health_data=health_data)
            except httpx.RequestError as e:
                pytest.skip(f"API health endpoint not accessible: {e}")

    @pytest.mark.asyncio
    async def test_api_docs_accessible(self, service_urls):
        """Test that API documentation is accessible."""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.get(service_urls["api_docs"])
                assert response.status_code == 200
                assert "swagger" in response.text.lower() or "openapi" in response.text.lower()
                logger.info("API documentation is accessible")
            except httpx.RequestError as e:
                pytest.skip(f"API documentation not accessible: {e}")

    @pytest.mark.asyncio
    async def test_service_management_api(self, service_urls):
        """Test the service management API endpoints."""
        base_url = f"http://api.pitas.localhost:8081/api/v1/services"
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # Test list services endpoint
                response = await client.get(f"{base_url}/")
                assert response.status_code == 200
                
                services_data = response.json()
                assert "services" in services_data
                assert "total_count" in services_data
                
                # Test health summary endpoint
                response = await client.get(f"{base_url}/health/summary")
                assert response.status_code == 200
                
                health_summary = response.json()
                assert "total_services" in health_summary
                assert "healthy_services" in health_summary
                
                logger.info("Service management API is accessible", 
                          total_services=health_summary.get("total_services"))
                
            except httpx.RequestError as e:
                pytest.skip(f"Service management API not accessible: {e}")

    @pytest.mark.asyncio
    async def test_grafana_accessible(self, service_urls):
        """Test that Grafana is accessible."""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.get(service_urls["grafana"])
                # Grafana might redirect to login, so accept 200 or 302
                assert response.status_code in [200, 302]
                logger.info("Grafana is accessible")
            except httpx.RequestError as e:
                pytest.skip(f"Grafana not accessible: {e}")

    @pytest.mark.asyncio
    async def test_prometheus_accessible(self, service_urls):
        """Test that Prometheus is accessible."""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.get(service_urls["prometheus"])
                assert response.status_code == 200
                logger.info("Prometheus is accessible")
            except httpx.RequestError as e:
                pytest.skip(f"Prometheus not accessible: {e}")

    @pytest.mark.asyncio
    async def test_neo4j_accessible(self, service_urls):
        """Test that Neo4j browser is accessible."""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.get(service_urls["neo4j"])
                assert response.status_code == 200
                logger.info("Neo4j browser is accessible")
            except httpx.RequestError as e:
                pytest.skip(f"Neo4j browser not accessible: {e}")

    @pytest.mark.asyncio
    async def test_influxdb_accessible(self, service_urls):
        """Test that InfluxDB is accessible."""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.get(service_urls["influxdb"])
                assert response.status_code == 200
                logger.info("InfluxDB is accessible")
            except httpx.RequestError as e:
                pytest.skip(f"InfluxDB not accessible: {e}")


class TestServiceHealthMonitoring:
    """Test suite for service health monitoring."""

    @pytest.mark.asyncio
    async def test_service_health_monitoring(self):
        """Test that service health monitoring works correctly."""
        # Register a test service
        test_service = ServiceRegistration(
            name="health-test-service",
            internal_port=8000,  # Use existing port for testing
            health_endpoint="/health"
        )
        
        await service_url_manager.register_service(test_service)
        
        # Wait a bit for health check to run
        await asyncio.sleep(2)
        
        # Check service health status
        service_info = service_url_manager.get_service_info("health-test-service")
        assert service_info is not None
        assert service_info.last_health_check is not None
        
        # Clean up
        await service_url_manager.deregister_service("health-test-service")

    @pytest.mark.asyncio
    async def test_service_url_generation(self):
        """Test that service URLs are generated correctly."""
        test_cases = [
            ("simple-service", "http://simple-service.pitas.localhost"),
            ("service_with_underscores", "http://service-with-underscores.pitas.localhost"),
            ("Service With Spaces", "http://service-with-spaces.pitas.localhost"),
            ("UPPERCASE-SERVICE", "http://uppercase-service.pitas.localhost")
        ]
        
        for service_name, expected_url in test_cases:
            test_service = ServiceRegistration(
                name=service_name,
                internal_port=9000
            )
            
            service_url = await service_url_manager.register_service(test_service)
            assert service_url == expected_url
            
            # Clean up
            await service_url_manager.deregister_service(service_name)


@pytest.mark.asyncio
async def test_end_to_end_service_workflow():
    """End-to-end test of the complete service workflow."""
    # 1. Register a service
    test_service = ServiceRegistration(
        name="e2e-test-service",
        internal_port=8000,
        health_endpoint="/health",
        tags=["e2e", "test"],
        metadata={"environment": "test"}
    )
    
    service_url = await service_url_manager.register_service(test_service)
    
    # 2. Verify service is listed
    services = service_url_manager.list_services()
    e2e_service = next((s for s in services if s["name"] == "e2e-test-service"), None)
    assert e2e_service is not None
    assert e2e_service["url"] == service_url
    
    # 3. Test service management API
    async with httpx.AsyncClient(timeout=10.0) as client:
        try:
            # List services via API
            response = await client.get("http://api.pitas.localhost:8081/api/v1/services/")
            if response.status_code == 200:
                api_services = response.json()["services"]
                api_e2e_service = next((s for s in api_services if s["name"] == "e2e-test-service"), None)
                assert api_e2e_service is not None
                
                # Get specific service info via API
                response = await client.get("http://api.pitas.localhost:8081/api/v1/services/e2e-test-service")
                if response.status_code == 200:
                    service_info = response.json()
                    assert service_info["name"] == "e2e-test-service"
                    assert service_info["internal_port"] == 8000
        except httpx.RequestError:
            # API might not be accessible in test environment
            pass
    
    # 4. Deregister service
    success = await service_url_manager.deregister_service("e2e-test-service")
    assert success
    
    # 5. Verify service is removed
    final_services = service_url_manager.list_services()
    final_e2e_service = next((s for s in final_services if s["name"] == "e2e-test-service"), None)
    assert final_e2e_service is None
