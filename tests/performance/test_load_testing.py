"""Performance and load testing for PITAS system."""

import asyncio
import time
import statistics
from typing import List, Dict, Any
import pytest
import httpx
from locust import HttpUser, task, between
from locust.env import Environment
from locust.stats import stats_printer, stats_history
from locust.log import setup_logging

from pitas.core.config import Settings


class PitasLoadTestUser(HttpUser):
    """Locust user class for load testing PITAS."""
    
    wait_time = between(1, 3)  # Wait 1-3 seconds between tasks
    
    def on_start(self):
        """Called when a user starts."""
        self.login()
    
    def login(self):
        """Authenticate user for testing."""
        response = self.client.post("/api/v1/auth/login", json={
            "username": "<EMAIL>",
            "password": "LoadTestPassword123!"
        })
        
        if response.status_code == 200:
            token = response.json().get("access_token")
            self.client.headers.update({"Authorization": f"Bearer {token}"})
    
    @task(3)
    def view_dashboard(self):
        """Load dashboard - most common operation."""
        self.client.get("/api/v1/dashboard")
    
    @task(2)
    def list_projects(self):
        """List projects."""
        self.client.get("/api/v1/projects/")
    
    @task(2)
    def list_vulnerabilities(self):
        """List vulnerabilities."""
        self.client.get("/api/v1/vulnerabilities/")
    
    @task(1)
    def list_pentesters(self):
        """List pentesters."""
        self.client.get("/api/v1/pentesters/")
    
    @task(1)
    def create_vulnerability(self):
        """Create a new vulnerability."""
        vulnerability_data = {
            "title": f"Load Test Vulnerability {time.time()}",
            "description": "Vulnerability created during load testing",
            "severity": "MEDIUM",
            "cvss_score": 5.5,
            "asset_id": "test-asset-id",
        }
        self.client.post("/api/v1/vulnerabilities/", json=vulnerability_data)
    
    @task(1)
    def search_vulnerabilities(self):
        """Search vulnerabilities."""
        self.client.get("/api/v1/vulnerabilities/search?q=sql+injection")
    
    @task(1)
    def get_vulnerability_metrics(self):
        """Get vulnerability metrics."""
        self.client.get("/api/v1/vulnerabilities/metrics")


class TestPerformance:
    """Performance testing suite."""
    
    @pytest.fixture
    def test_client(self):
        """HTTP client for performance testing."""
        return httpx.AsyncClient(
            base_url="http://localhost:8000",
            timeout=30.0
        )
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_api_response_times(self, test_client):
        """Test API response times for critical endpoints."""
        
        # Login first to get authentication token
        login_response = await test_client.post("/api/v1/auth/login", json={
            "username": "<EMAIL>",
            "password": "TestPassword123!"
        })
        
        if login_response.status_code == 200:
            token = login_response.json().get("access_token")
            headers = {"Authorization": f"Bearer {token}"}
        else:
            headers = {}
        
        # Define critical endpoints and their performance thresholds (in seconds)
        endpoints = {
            "/api/v1/health": 0.1,  # Health check should be very fast
            "/api/v1/dashboard": 2.0,  # Dashboard should load within 2 seconds
            "/api/v1/projects/": 1.5,  # Project list should load within 1.5 seconds
            "/api/v1/vulnerabilities/": 2.0,  # Vulnerability list within 2 seconds
            "/api/v1/pentesters/": 1.0,  # Pentester list within 1 second
            "/api/v1/vulnerabilities/metrics": 3.0,  # Metrics can take up to 3 seconds
        }
        
        results = {}
        
        for endpoint, threshold in endpoints.items():
            # Measure response time over multiple requests
            response_times = []
            
            for _ in range(5):  # Test each endpoint 5 times
                start_time = time.time()
                
                try:
                    response = await test_client.get(endpoint, headers=headers)
                    end_time = time.time()
                    
                    response_time = end_time - start_time
                    response_times.append(response_time)
                    
                    # Verify response is successful
                    assert response.status_code in [200, 401], f"Unexpected status for {endpoint}: {response.status_code}"
                    
                except Exception as e:
                    pytest.fail(f"Request to {endpoint} failed: {e}")
            
            # Calculate statistics
            avg_response_time = statistics.mean(response_times)
            max_response_time = max(response_times)
            min_response_time = min(response_times)
            
            results[endpoint] = {
                "avg": avg_response_time,
                "max": max_response_time,
                "min": min_response_time,
                "threshold": threshold,
            }
            
            # Assert performance requirements
            assert avg_response_time < threshold, (
                f"{endpoint} average response time {avg_response_time:.3f}s "
                f"exceeds threshold {threshold}s"
            )
            
            assert max_response_time < threshold * 2, (
                f"{endpoint} max response time {max_response_time:.3f}s "
                f"exceeds 2x threshold {threshold * 2}s"
            )
        
        # Print performance summary
        print("\nPerformance Test Results:")
        print("-" * 60)
        for endpoint, stats in results.items():
            print(f"{endpoint:30} | Avg: {stats['avg']:.3f}s | Max: {stats['max']:.3f}s | Threshold: {stats['threshold']}s")
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_concurrent_requests(self, test_client):
        """Test system performance under concurrent load."""
        
        async def make_request(endpoint: str, headers: dict = None) -> Dict[str, Any]:
            """Make a single request and measure performance."""
            start_time = time.time()
            try:
                response = await test_client.get(endpoint, headers=headers or {})
                end_time = time.time()
                
                return {
                    "endpoint": endpoint,
                    "status_code": response.status_code,
                    "response_time": end_time - start_time,
                    "success": response.status_code < 400,
                }
            except Exception as e:
                end_time = time.time()
                return {
                    "endpoint": endpoint,
                    "status_code": 0,
                    "response_time": end_time - start_time,
                    "success": False,
                    "error": str(e),
                }
        
        # Test concurrent requests to different endpoints
        endpoints = [
            "/api/v1/health",
            "/api/v1/projects/",
            "/api/v1/vulnerabilities/",
            "/api/v1/pentesters/",
        ]
        
        # Create 50 concurrent requests (mix of endpoints)
        tasks = []
        for i in range(50):
            endpoint = endpoints[i % len(endpoints)]
            task = make_request(endpoint)
            tasks.append(task)
        
        # Execute all requests concurrently
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        total_time = end_time - start_time
        
        # Analyze results
        successful_requests = [r for r in results if isinstance(r, dict) and r.get("success", False)]
        failed_requests = [r for r in results if not (isinstance(r, dict) and r.get("success", False))]
        
        success_rate = len(successful_requests) / len(results)
        avg_response_time = statistics.mean([r["response_time"] for r in successful_requests])
        
        # Performance assertions
        assert success_rate >= 0.95, f"Success rate {success_rate:.2%} below 95% threshold"
        assert total_time < 10.0, f"Total concurrent execution time {total_time:.2f}s exceeds 10s"
        assert avg_response_time < 5.0, f"Average response time {avg_response_time:.3f}s exceeds 5s"
        
        print(f"\nConcurrent Load Test Results:")
        print(f"Total requests: {len(results)}")
        print(f"Successful requests: {len(successful_requests)}")
        print(f"Failed requests: {len(failed_requests)}")
        print(f"Success rate: {success_rate:.2%}")
        print(f"Total execution time: {total_time:.2f}s")
        print(f"Average response time: {avg_response_time:.3f}s")
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_database_query_performance(self, test_client):
        """Test database query performance."""
        
        # Test endpoints that involve complex database queries
        complex_endpoints = {
            "/api/v1/vulnerabilities/search?q=sql": 3.0,  # Search queries
            "/api/v1/projects/statistics/overview": 2.0,  # Statistics aggregation
            "/api/v1/pentesters/performance/metrics": 2.5,  # Performance metrics
            "/api/v1/vulnerabilities/metrics": 3.0,  # Vulnerability metrics
        }
        
        for endpoint, threshold in complex_endpoints.items():
            response_times = []
            
            for _ in range(3):  # Test each complex query 3 times
                start_time = time.time()
                
                try:
                    response = await test_client.get(endpoint)
                    end_time = time.time()
                    
                    response_time = end_time - start_time
                    response_times.append(response_time)
                    
                    # Verify response (may be 401 if auth required, but should not timeout)
                    assert response.status_code in [200, 401, 404], (
                        f"Unexpected status for {endpoint}: {response.status_code}"
                    )
                    
                except httpx.TimeoutException:
                    pytest.fail(f"Query timeout for {endpoint}")
                except Exception as e:
                    pytest.fail(f"Query failed for {endpoint}: {e}")
            
            avg_response_time = statistics.mean(response_times)
            max_response_time = max(response_times)
            
            # Assert database query performance
            assert avg_response_time < threshold, (
                f"Database query {endpoint} average time {avg_response_time:.3f}s "
                f"exceeds threshold {threshold}s"
            )
            
            print(f"DB Query {endpoint}: Avg {avg_response_time:.3f}s, Max {max_response_time:.3f}s")
    
    @pytest.mark.performance
    @pytest.mark.slow
    def test_locust_load_testing(self):
        """Run Locust-based load testing."""
        
        # Setup Locust environment
        setup_logging("INFO", None)
        
        env = Environment(user_classes=[PitasLoadTestUser])
        env.create_local_runner()
        
        # Start load test
        env.runner.start(user_count=10, spawn_rate=2)
        
        # Run for 60 seconds
        import gevent
        gevent.spawn(stats_printer(env.stats))
        gevent.spawn(stats_history, env.runner)
        
        # Let the test run
        gevent.sleep(60)
        
        # Stop the test
        env.runner.stop()
        
        # Analyze results
        stats = env.stats.total
        
        # Performance assertions
        assert stats.avg_response_time < 2000, f"Average response time {stats.avg_response_time}ms exceeds 2000ms"
        assert stats.failure_count / stats.num_requests < 0.05, f"Failure rate {stats.failure_count / stats.num_requests:.2%} exceeds 5%"
        
        print(f"\nLocust Load Test Results:")
        print(f"Total requests: {stats.num_requests}")
        print(f"Failed requests: {stats.failure_count}")
        print(f"Average response time: {stats.avg_response_time}ms")
        print(f"95th percentile: {stats.get_response_time_percentile(0.95)}ms")
        print(f"Requests per second: {stats.total_rps:.2f}")
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_memory_usage_under_load(self, test_client):
        """Test memory usage during high load operations."""
        import psutil
        import os
        
        # Get current process
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Perform memory-intensive operations
        tasks = []
        for i in range(100):
            # Create requests that might consume memory
            task = test_client.get("/api/v1/vulnerabilities/")
            tasks.append(task)
        
        # Execute all requests
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # Check memory usage after operations
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Memory should not increase dramatically
        assert memory_increase < 100, f"Memory increased by {memory_increase:.2f}MB, exceeding 100MB threshold"
        
        print(f"Memory usage: Initial {initial_memory:.2f}MB, Final {final_memory:.2f}MB, Increase {memory_increase:.2f}MB")
