Feature: Vulnerability Assessment Security Testing
  As a security engineer
  I want to ensure vulnerability assessments are accurate and secure
  So that critical vulnerabilities are identified within SLA requirements

  Background:
    Given the vulnerability assessment system is running
    And the threat intelligence feeds are active
    And the security scanning tools are configured

  @security @bdd @critical
  Scenario: Critical Asset Vulnerability Assessment
    Given a vulnerability assessment request for critical asset "production-database"
    And the asset has criticality level "CRITICAL"
    And the assessment scope is "FULL"
    When the automated scanner processes the request
    Then all critical vulnerabilities should be identified within SLA
    And the scan duration should be less than 4 hours
    And the false positive rate should be less than 10%
    And the results should include CVSS scores
    And the results should include MITRE ATT&CK mappings

  @security @bdd @integration
  Scenario: Multi-Framework Security Validation
    Given a vulnerability assessment for asset "web-application"
    When the assessment is processed through multiple frameworks
    Then CVSS 4.0 scores should be calculated correctly
    And MITRE ATT&CK techniques should be mapped
    And NIST CSF 2.0 controls should be identified
    And the correlation between frameworks should be validated

  @security @bdd @performance
  Scenario: High-Load Vulnerability Scanning
    Given 100 concurrent vulnerability assessment requests
    And each request targets different asset types
    When the scanning system processes all requests
    Then all scans should complete within performance SLA
    And system resources should remain within acceptable limits
    And no scan results should be lost or corrupted
    And the scanning queue should process efficiently

  @security @bdd @regression
  Scenario: Security Control Effectiveness Validation
    Given a previously identified critical vulnerability
    And a remediation has been applied
    When a regression scan is performed
    Then the vulnerability should no longer be detected
    And no new vulnerabilities should be introduced
    And the security control should be validated as effective
    And the remediation should be marked as successful

  @security @bdd @threat_modeling
  Scenario: Threat Intelligence Integration
    Given threat intelligence feeds are providing new IOCs
    And the vulnerability database is updated
    When an assessment is performed on affected assets
    Then new threats should be detected and correlated
    And threat actor attribution should be provided
    And the risk assessment should be updated accordingly
    And alerts should be generated for critical threats

  @security @bdd @compliance
  Scenario: Compliance Framework Validation
    Given an asset subject to SOC 2 compliance requirements
    When a compliance-focused vulnerability assessment is performed
    Then all SOC 2 security controls should be validated
    And compliance gaps should be identified
    And remediation recommendations should align with SOC 2
    And audit trail should be maintained for compliance reporting
