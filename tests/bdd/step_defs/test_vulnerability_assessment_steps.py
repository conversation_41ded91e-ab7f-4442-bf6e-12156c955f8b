"""BDD step definitions for vulnerability assessment security testing."""

import asyncio
from datetime import datetime, timedelta
from typing import Any, Dict, List

import pytest
from pytest_bdd import given, when, then, scenarios
from testcontainers.postgres import PostgresContainer
from testcontainers.redis import RedisContainer

from pitas.db.models.vulnerability import (
    Vulnerability,
    Asset,
    AssetCriticality,
    VulnerabilitySeverity,
)
from pitas.services.vulnerability_service import VulnerabilityService
from pitas.schemas.vulnerability import VulnerabilityAssessmentRequest, AssessmentScope


# Load scenarios from feature file
scenarios("../features/vulnerability_assessment.feature")


@pytest.fixture(scope="session")
def postgres_container():
    """Provide PostgreSQL container for testing."""
    with PostgresContainer("postgres:15") as postgres:
        yield postgres


@pytest.fixture(scope="session")
def redis_container():
    """Provide Redis container for testing."""
    with RedisContainer("redis:7") as redis:
        yield redis


@pytest.fixture
async def vulnerability_service(postgres_container, redis_container):
    """Provide vulnerability service with test containers."""
    # Initialize service with test database
    service = VulnerabilityService(
        db_url=postgres_container.get_connection_url(),
        redis_url=redis_container.get_connection_url(),
    )
    await service.initialize()
    return service


@given("the vulnerability assessment system is running")
async def step_system_running(context):
    """Ensure the vulnerability assessment system is operational."""
    context.system_start_time = datetime.utcnow()
    context.system_status = "running"
    assert context.system_status == "running"


@given("the threat intelligence feeds are active")
async def step_threat_feeds_active(context):
    """Ensure threat intelligence feeds are operational."""
    context.threat_feeds = {
        "mitre_attack": True,
        "cve_feeds": True,
        "threat_intel": True,
        "ioc_feeds": True,
    }
    assert all(context.threat_feeds.values())


@given("the security scanning tools are configured")
async def step_scanning_tools_configured(context):
    """Ensure security scanning tools are properly configured."""
    context.scanning_tools = {
        "nessus": {"status": "active", "version": "10.6.0"},
        "openvas": {"status": "active", "version": "22.4"},
        "nuclei": {"status": "active", "version": "3.0.0"},
        "nmap": {"status": "active", "version": "7.94"},
    }
    assert all(tool["status"] == "active" for tool in context.scanning_tools.values())


@given('a vulnerability assessment request for critical asset "{asset_name}"')
async def step_assessment_request_critical_asset(context, asset_name: str):
    """Create a vulnerability assessment request for a critical asset."""
    context.asset = Asset(
        name=asset_name,
        asset_type="database",
        criticality=AssetCriticality.CRITICAL,
        ip_address="**********",
        hostname=f"{asset_name}.internal.com",
    )
    context.assessment_request = VulnerabilityAssessmentRequest(
        asset_id=context.asset.id,
        scope=AssessmentScope.FULL,
        priority="CRITICAL",
        requested_by="security_team",
    )


@given('the asset has criticality level "{criticality}"')
async def step_asset_criticality(context, criticality: str):
    """Set the asset criticality level."""
    context.asset.criticality = AssetCriticality[criticality]
    assert context.asset.criticality.value == criticality


@given('the assessment scope is "{scope}"')
async def step_assessment_scope(context, scope: str):
    """Set the assessment scope."""
    context.assessment_request.scope = AssessmentScope[scope]
    assert context.assessment_request.scope.value == scope


@when("the automated scanner processes the request")
async def step_automated_scanning(context, vulnerability_service):
    """Process the vulnerability assessment request through automated scanning."""
    context.scan_start_time = datetime.utcnow()
    
    # Simulate vulnerability scanning
    context.scan_results = await vulnerability_service.perform_assessment(
        context.assessment_request
    )
    
    context.scan_end_time = datetime.utcnow()
    context.scan_duration = context.scan_end_time - context.scan_start_time


@then("all critical vulnerabilities should be identified within SLA")
async def step_validate_critical_vulnerabilities(context):
    """Validate that all critical vulnerabilities are identified within SLA."""
    critical_vulns = [
        vuln for vuln in context.scan_results.vulnerabilities
        if vuln.severity >= VulnerabilitySeverity.HIGH
    ]
    
    # Ensure critical vulnerabilities are found
    assert len(critical_vulns) > 0, "No critical vulnerabilities identified"
    
    # Validate each critical vulnerability has required fields
    for vuln in critical_vulns:
        assert vuln.cvss_score is not None
        assert vuln.cvss_score >= 7.0  # High/Critical CVSS threshold
        assert vuln.mitre_attack_techniques is not None
        assert len(vuln.mitre_attack_techniques) > 0


@then("the scan duration should be less than 4 hours")
async def step_validate_scan_duration(context):
    """Validate that the scan completes within the 4-hour SLA."""
    max_duration = timedelta(hours=4)
    assert context.scan_duration < max_duration, (
        f"Scan took {context.scan_duration}, exceeding 4-hour SLA"
    )


@then("the false positive rate should be less than 10%")
async def step_validate_false_positive_rate(context):
    """Validate that the false positive rate is acceptable."""
    total_findings = len(context.scan_results.vulnerabilities)
    false_positives = len([
        vuln for vuln in context.scan_results.vulnerabilities
        if vuln.is_false_positive
    ])
    
    if total_findings > 0:
        false_positive_rate = false_positives / total_findings
        assert false_positive_rate < 0.1, (
            f"False positive rate {false_positive_rate:.2%} exceeds 10% threshold"
        )


@then("the results should include CVSS scores")
async def step_validate_cvss_scores(context):
    """Validate that all vulnerabilities have CVSS scores."""
    for vuln in context.scan_results.vulnerabilities:
        assert vuln.cvss_score is not None
        assert 0.0 <= vuln.cvss_score <= 10.0
        assert vuln.cvss_vector is not None


@then("the results should include MITRE ATT&CK mappings")
async def step_validate_mitre_mappings(context):
    """Validate that vulnerabilities are mapped to MITRE ATT&CK techniques."""
    high_severity_vulns = [
        vuln for vuln in context.scan_results.vulnerabilities
        if vuln.severity >= VulnerabilitySeverity.MEDIUM
    ]
    
    for vuln in high_severity_vulns:
        assert vuln.mitre_attack_techniques is not None
        assert len(vuln.mitre_attack_techniques) > 0
        
        # Validate technique format (e.g., T1190, T1068)
        for technique in vuln.mitre_attack_techniques:
            assert technique.startswith("T")
            assert len(technique) >= 5  # T1234 minimum format


@given('a vulnerability assessment for asset "{asset_name}"')
async def step_assessment_for_asset(context, asset_name: str):
    """Create a vulnerability assessment for the specified asset."""
    context.asset = Asset(
        name=asset_name,
        asset_type="web_application",
        criticality=AssetCriticality.HIGH,
        ip_address="**********",
        hostname=f"{asset_name}.example.com",
    )
    context.assessment_request = VulnerabilityAssessmentRequest(
        asset_id=context.asset.id,
        scope=AssessmentScope.FULL,
        priority="HIGH",
    )


@when("the assessment is processed through multiple frameworks")
async def step_multi_framework_processing(context, vulnerability_service):
    """Process assessment through multiple security frameworks."""
    context.scan_results = await vulnerability_service.multi_framework_assessment(
        context.assessment_request,
        frameworks=["cvss", "mitre_attack", "nist_csf"]
    )


@then("CVSS 4.0 scores should be calculated correctly")
async def step_validate_cvss_4_scores(context):
    """Validate CVSS 4.0 score calculations."""
    for vuln in context.scan_results.vulnerabilities:
        if vuln.cvss_version == "4.0":
            assert vuln.cvss_score is not None
            assert 0.0 <= vuln.cvss_score <= 10.0
            assert vuln.cvss_vector is not None
            assert "CVSS:4.0/" in vuln.cvss_vector


@then("MITRE ATT&CK techniques should be mapped")
async def step_validate_attack_mapping(context):
    """Validate MITRE ATT&CK technique mapping."""
    for vuln in context.scan_results.vulnerabilities:
        if vuln.severity >= VulnerabilitySeverity.MEDIUM:
            assert vuln.mitre_attack_techniques is not None
            assert len(vuln.mitre_attack_techniques) > 0


@then("NIST CSF 2.0 controls should be identified")
async def step_validate_nist_controls(context):
    """Validate NIST CSF 2.0 control identification."""
    for vuln in context.scan_results.vulnerabilities:
        if vuln.severity >= VulnerabilitySeverity.HIGH:
            assert vuln.nist_csf_controls is not None
            assert len(vuln.nist_csf_controls) > 0


@then("the correlation between frameworks should be validated")
async def step_validate_framework_correlation(context):
    """Validate correlation between different security frameworks."""
    for vuln in context.scan_results.vulnerabilities:
        # High CVSS scores should correlate with critical MITRE techniques
        if vuln.cvss_score and vuln.cvss_score >= 9.0:
            assert vuln.mitre_attack_techniques is not None
            assert len(vuln.mitre_attack_techniques) > 0
            
        # Critical vulnerabilities should have NIST controls
        if vuln.severity == VulnerabilitySeverity.CRITICAL:
            assert vuln.nist_csf_controls is not None
            assert len(vuln.nist_csf_controls) > 0
