"""Tests for Phase 12: Continuous Improvement and Innovation."""

import pytest
from datetime import datetime, timedelta
from uuid import uuid4

from pitas.db.models.feedback import (
    UserFeedback, FeedbackType, FeedbackStatus, SentimentScore
)
from pitas.db.models.innovation import (
    InnovationProject, InnovationStatus, TechnologyCategory, PriorityLevel
)
from pitas.db.models.analytics import (
    ABTestExperiment, ExperimentStatus, ExperimentType, MetricType
)
from pitas.schemas.feedback import (
    UserFeedbackCreate, FeedbackResponseCreate, UserBehaviorAnalyticsCreate
)
from pitas.schemas.innovation import (
    InnovationProjectCreate, InnovationEvaluationCreate
)
from pitas.schemas.analytics import (
    ABTestExperimentCreate, ExperimentMetricCreate
)
from pitas.services.feedback import FeedbackService
from pitas.services.innovation import InnovationService
from pitas.services.analytics import AnalyticsService


class TestFeedbackService:
    """Test feedback collection and analysis service."""

    @pytest.mark.asyncio
    async def test_create_feedback(self, db_session, sample_user):
        """Test creating user feedback."""
        service = FeedbackService(db_session)
        
        feedback_data = UserFeedbackCreate(
            feedback_type=FeedbackType.FEATURE_REQUEST,
            title="Improve dashboard performance",
            description="The dashboard loads slowly and needs optimization",
            rating=3,
            feature_area="dashboard",
            workflow_step="initial_load"
        )
        
        feedback = await service.create_feedback(sample_user.id, feedback_data)
        
        assert feedback.id is not None
        assert feedback.user_id == sample_user.id
        assert feedback.title == "Improve dashboard performance"
        assert feedback.feedback_type == FeedbackType.FEATURE_REQUEST
        assert feedback.status == FeedbackStatus.SUBMITTED
        assert feedback.sentiment_score is not None
        assert feedback.priority_score is not None

    @pytest.mark.asyncio
    async def test_respond_to_feedback(self, db_session, sample_user, sample_feedback):
        """Test responding to user feedback."""
        service = FeedbackService(db_session)
        
        response_data = FeedbackResponseCreate(
            feedback_id=sample_feedback.id,
            response_text="Thank you for the feedback. We'll investigate this issue.",
            is_public=True
        )
        
        response = await service.respond_to_feedback(
            sample_feedback.id, sample_user.id, response_data
        )
        
        assert response.id is not None
        assert response.feedback_id == sample_feedback.id
        assert response.responder_id == sample_user.id
        assert response.response_text == "Thank you for the feedback. We'll investigate this issue."

    @pytest.mark.asyncio
    async def test_update_feedback_status(self, db_session, sample_feedback):
        """Test updating feedback status."""
        service = FeedbackService(db_session)
        
        updated_feedback = await service.update_feedback_status(
            sample_feedback.id,
            FeedbackStatus.IN_PROGRESS,
            resolution_notes="Working on dashboard optimization"
        )
        
        assert updated_feedback.status == FeedbackStatus.IN_PROGRESS
        assert updated_feedback.resolution_notes == "Working on dashboard optimization"

    @pytest.mark.asyncio
    async def test_track_user_behavior(self, db_session, sample_user):
        """Test tracking user behavior analytics."""
        service = FeedbackService(db_session)
        
        analytics_data = UserBehaviorAnalyticsCreate(
            session_id="session_123",
            event_type="page_view",
            event_data={"page": "dashboard", "action": "load"},
            page_url="/dashboard",
            user_agent="Mozilla/5.0...",
            page_load_time=2.5,
            time_on_page=120.0
        )
        
        analytics = await service.track_user_behavior(sample_user.id, analytics_data)
        
        assert analytics.id is not None
        assert analytics.user_id == sample_user.id
        assert analytics.session_id == "session_123"
        assert analytics.event_type == "page_view"
        assert analytics.page_load_time == 2.5

    @pytest.mark.asyncio
    async def test_get_feedback_analytics(self, db_session):
        """Test getting feedback analytics."""
        service = FeedbackService(db_session)
        
        analytics = await service.get_feedback_analytics()
        
        assert analytics.total_feedback >= 0
        assert isinstance(analytics.feedback_by_type, dict)
        assert isinstance(analytics.feedback_by_status, dict)
        assert isinstance(analytics.sentiment_distribution, dict)


class TestInnovationService:
    """Test innovation management service."""

    @pytest.mark.asyncio
    async def test_create_innovation_project(self, db_session, sample_user):
        """Test creating innovation project."""
        service = InnovationService(db_session)
        
        project_data = InnovationProjectCreate(
            title="AI-Powered Vulnerability Detection",
            description="Implement machine learning for automated vulnerability detection",
            category=TechnologyCategory.ARTIFICIAL_INTELLIGENCE,
            priority=PriorityLevel.HIGH,
            business_value="Reduce manual effort and improve detection accuracy",
            expected_roi=3.5,
            estimated_cost=50000.0,
            estimated_timeline_weeks=12
        )
        
        project = await service.create_innovation_project(sample_user.id, project_data)
        
        assert project.id is not None
        assert project.proposed_by == sample_user.id
        assert project.title == "AI-Powered Vulnerability Detection"
        assert project.category == TechnologyCategory.ARTIFICIAL_INTELLIGENCE
        assert project.priority == PriorityLevel.HIGH
        assert project.status == InnovationStatus.IDEA
        assert project.overall_score is not None

    @pytest.mark.asyncio
    async def test_evaluate_project(self, db_session, sample_user, sample_innovation_project):
        """Test evaluating innovation project."""
        service = InnovationService(db_session)
        
        evaluation_data = InnovationEvaluationCreate(
            project_id=sample_innovation_project.id,
            technical_feasibility=8.0,
            business_impact=9.0,
            implementation_effort=6.0,
            risk_assessment=4.0,
            strategic_alignment=8.5,
            recommendation="approve",
            strengths="Strong business case and technical feasibility",
            recommendations="Proceed with proof of concept"
        )
        
        evaluation = await service.evaluate_project(
            sample_innovation_project.id, sample_user.id, evaluation_data
        )
        
        assert evaluation.id is not None
        assert evaluation.project_id == sample_innovation_project.id
        assert evaluation.evaluator_id == sample_user.id
        assert evaluation.technical_feasibility == 8.0
        assert evaluation.recommendation == "approve"

    @pytest.mark.asyncio
    async def test_update_project_status(self, db_session, sample_innovation_project):
        """Test updating innovation project status."""
        service = InnovationService(db_session)
        
        updated_project = await service.update_project_status(
            sample_innovation_project.id,
            InnovationStatus.PROOF_OF_CONCEPT
        )
        
        assert updated_project.status == InnovationStatus.PROOF_OF_CONCEPT
        assert updated_project.started_at is not None

    @pytest.mark.asyncio
    async def test_get_innovation_pipeline(self, db_session):
        """Test getting innovation pipeline overview."""
        service = InnovationService(db_session)
        
        pipeline = await service.get_innovation_pipeline()
        
        assert pipeline.total_projects >= 0
        assert isinstance(pipeline.projects_by_status, dict)
        assert isinstance(pipeline.projects_by_category, dict)
        assert isinstance(pipeline.projects_by_priority, dict)


class TestAnalyticsService:
    """Test analytics and A/B testing service."""

    @pytest.mark.asyncio
    async def test_create_ab_test(self, db_session, sample_user):
        """Test creating A/B test experiment."""
        service = AnalyticsService(db_session)
        
        experiment_data = ABTestExperimentCreate(
            name="Dashboard Layout Test",
            description="Test new dashboard layout vs current layout",
            hypothesis="New layout will improve user engagement by 15%",
            experiment_type=ExperimentType.AB_TEST,
            control_variant={"layout": "current"},
            test_variants=[{"layout": "new_design"}],
            primary_metric=MetricType.ENGAGEMENT,
            success_criteria="15% improvement in engagement metrics",
            confidence_level=95.0,
            minimum_detectable_effect=15.0
        )
        
        experiment = await service.create_ab_test(sample_user.id, experiment_data)
        
        assert experiment.id is not None
        assert experiment.created_by == sample_user.id
        assert experiment.name == "Dashboard Layout Test"
        assert experiment.experiment_type == ExperimentType.AB_TEST
        assert experiment.status == ExperimentStatus.DRAFT
        assert experiment.sample_size_required is not None

    @pytest.mark.asyncio
    async def test_start_experiment(self, db_session, sample_ab_experiment):
        """Test starting A/B test experiment."""
        service = AnalyticsService(db_session)
        
        started_experiment = await service.start_experiment(sample_ab_experiment.id)
        
        assert started_experiment.status == ExperimentStatus.ACTIVE
        assert started_experiment.start_date is not None

    @pytest.mark.asyncio
    async def test_assign_user_to_experiment(self, db_session, sample_user, sample_ab_experiment):
        """Test assigning user to experiment variant."""
        service = AnalyticsService(db_session)
        
        # First start the experiment
        await service.start_experiment(sample_ab_experiment.id)
        
        participant = await service.assign_user_to_experiment(
            sample_ab_experiment.id, sample_user.id
        )
        
        assert participant.id is not None
        assert participant.experiment_id == sample_ab_experiment.id
        assert participant.user_id == sample_user.id
        assert participant.variant_assigned in ['control', 'variant_0']

    @pytest.mark.asyncio
    async def test_record_experiment_metric(self, db_session, sample_experiment_participant):
        """Test recording experiment metric."""
        service = AnalyticsService(db_session)
        
        metric_data = ExperimentMetricCreate(
            experiment_id=sample_experiment_participant.experiment_id,
            participant_id=sample_experiment_participant.id,
            metric_type=MetricType.ENGAGEMENT,
            metric_value=1.0,
            variant=sample_experiment_participant.variant_assigned,
            session_id="session_123"
        )
        
        metric = await service.record_experiment_metric(metric_data)
        
        assert metric.id is not None
        assert metric.experiment_id == sample_experiment_participant.experiment_id
        assert metric.participant_id == sample_experiment_participant.id
        assert metric.metric_type == MetricType.ENGAGEMENT
        assert metric.metric_value == 1.0

    @pytest.mark.asyncio
    async def test_analyze_experiment_results(self, db_session, sample_ab_experiment):
        """Test analyzing experiment results."""
        service = AnalyticsService(db_session)
        
        results = await service.analyze_experiment_results(sample_ab_experiment.id)
        
        assert results.experiment_id == sample_ab_experiment.id
        assert results.total_participants >= 0
        assert isinstance(results.participants_by_variant, dict)
        assert isinstance(results.conversion_rates, dict)
        assert isinstance(results.statistical_significance, bool)
        assert results.recommendation is not None

    @pytest.mark.asyncio
    async def test_generate_improvement_recommendations(self, db_session):
        """Test generating improvement recommendations."""
        service = AnalyticsService(db_session)
        
        recommendations = await service.generate_improvement_recommendations(limit=5)
        
        assert len(recommendations) <= 5
        for rec in recommendations:
            assert rec.id is not None
            assert rec.title is not None
            assert rec.description is not None
            assert rec.category is not None
            assert 1.0 <= rec.priority_score <= 10.0
            assert 1.0 <= rec.impact_score <= 10.0
            assert 1.0 <= rec.effort_score <= 10.0
            assert 0.0 <= rec.confidence_score <= 1.0

    @pytest.mark.asyncio
    async def test_get_analytics_dashboard(self, db_session):
        """Test getting analytics dashboard data."""
        service = AnalyticsService(db_session)
        
        dashboard = await service.get_analytics_dashboard()
        
        assert dashboard.active_experiments >= 0
        assert dashboard.completed_experiments >= 0
        assert dashboard.performance_benchmarks >= 0
        assert dashboard.improvement_recommendations >= 0
