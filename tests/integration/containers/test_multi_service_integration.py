"""Multi-service integration tests using TestContainers."""

import asyncio
import pytest
import httpx
from testcontainers.postgres import PostgresContainer
from testcontainers.redis import RedisContainer
from testcontainers.compose import DockerCompose
from testcontainers.generic import GenericContainer

from pitas.core.config import Settings
from pitas.db.database import get_database
from pitas.services.vulnerability_service import VulnerabilityService
from pitas.services.pentester_service import PentesterService
from pitas.services.project_service import ProjectService


@pytest.fixture(scope="session")
def postgres_container():
    """PostgreSQL container for integration testing."""
    with PostgresContainer(
        "postgres:15",
        username="test_user",
        password="test_password",
        dbname="test_pitas",
        port=5432
    ) as postgres:
        yield postgres


@pytest.fixture(scope="session")
def redis_container():
    """Redis container for caching and session management."""
    with RedisContainer("redis:7-alpine") as redis:
        yield redis


@pytest.fixture(scope="session")
def neo4j_container():
    """Neo4j container for graph database operations."""
    with GenericContainer("neo4j:5.14") as neo4j:
        neo4j.with_exposed_ports(7474, 7687)
        neo4j.with_env("NEO4J_AUTH", "neo4j/testpassword")
        neo4j.with_env("NEO4J_PLUGINS", '["apoc"]')
        yield neo4j


@pytest.fixture(scope="session")
def influxdb_container():
    """InfluxDB container for time-series data."""
    with GenericContainer("influxdb:2.7") as influxdb:
        influxdb.with_exposed_ports(8086)
        influxdb.with_env("INFLUXDB_DB", "pitas_metrics")
        influxdb.with_env("INFLUXDB_ADMIN_USER", "admin")
        influxdb.with_env("INFLUXDB_ADMIN_PASSWORD", "testpassword")
        yield influxdb


@pytest.fixture(scope="session")
def test_environment(postgres_container, redis_container, neo4j_container, influxdb_container):
    """Complete test environment with all services."""
    return {
        "postgres": postgres_container,
        "redis": redis_container,
        "neo4j": neo4j_container,
        "influxdb": influxdb_container,
    }


@pytest.fixture
async def test_settings(test_environment):
    """Test settings with container connection strings."""
    postgres = test_environment["postgres"]
    redis = test_environment["redis"]
    neo4j = test_environment["neo4j"]
    influxdb = test_environment["influxdb"]
    
    settings = Settings(
        database_url=postgres.get_connection_url(),
        redis_url=redis.get_connection_url(),
        neo4j_uri=f"bolt://localhost:{neo4j.get_exposed_port(7687)}",
        neo4j_user="neo4j",
        neo4j_password="testpassword",
        influxdb_url=f"http://localhost:{influxdb.get_exposed_port(8086)}",
        influxdb_token="test-token",
        influxdb_org="test-org",
        influxdb_bucket="pitas-metrics",
    )
    
    return settings


@pytest.fixture
async def database_session(test_settings):
    """Database session for testing."""
    database = get_database(test_settings.database_url)
    async with database.session() as session:
        yield session


@pytest.fixture
async def vulnerability_service(test_settings, database_session):
    """Vulnerability service with test configuration."""
    service = VulnerabilityService(
        db_session=database_session,
        settings=test_settings
    )
    await service.initialize()
    return service


@pytest.fixture
async def pentester_service(test_settings, database_session):
    """Pentester service with test configuration."""
    service = PentesterService(
        db_session=database_session,
        settings=test_settings
    )
    await service.initialize()
    return service


@pytest.fixture
async def project_service(test_settings, database_session):
    """Project service with test configuration."""
    service = ProjectService(
        db_session=database_session,
        settings=test_settings
    )
    await service.initialize()
    return service


class TestMultiServiceIntegration:
    """Integration tests for multi-service interactions."""
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_database_connectivity(self, test_environment):
        """Test database connectivity and basic operations."""
        postgres = test_environment["postgres"]
        
        # Test connection
        connection_url = postgres.get_connection_url()
        assert connection_url is not None
        
        # Test basic query
        import asyncpg
        conn = await asyncpg.connect(connection_url)
        try:
            result = await conn.fetchval("SELECT 1")
            assert result == 1
        finally:
            await conn.close()
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_redis_connectivity(self, test_environment):
        """Test Redis connectivity and caching operations."""
        redis_container = test_environment["redis"]
        
        import redis.asyncio as redis
        
        redis_client = redis.from_url(redis_container.get_connection_url())
        
        try:
            # Test basic operations
            await redis_client.set("test_key", "test_value")
            value = await redis_client.get("test_key")
            assert value.decode() == "test_value"
            
            # Test expiration
            await redis_client.setex("expire_key", 1, "expire_value")
            await asyncio.sleep(1.1)
            expired_value = await redis_client.get("expire_key")
            assert expired_value is None
            
        finally:
            await redis_client.close()
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_neo4j_connectivity(self, test_environment):
        """Test Neo4j connectivity and graph operations."""
        neo4j_container = test_environment["neo4j"]
        
        from neo4j import AsyncGraphDatabase
        
        uri = f"bolt://localhost:{neo4j_container.get_exposed_port(7687)}"
        driver = AsyncGraphDatabase.driver(uri, auth=("neo4j", "testpassword"))
        
        try:
            async with driver.session() as session:
                # Test basic query
                result = await session.run("RETURN 1 as number")
                record = await result.single()
                assert record["number"] == 1
                
                # Test node creation and retrieval
                await session.run(
                    "CREATE (n:TestNode {name: $name})",
                    name="integration_test"
                )
                
                result = await session.run(
                    "MATCH (n:TestNode {name: $name}) RETURN n",
                    name="integration_test"
                )
                record = await result.single()
                assert record["n"]["name"] == "integration_test"
                
                # Cleanup
                await session.run("MATCH (n:TestNode) DELETE n")
                
        finally:
            await driver.close()
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_service_layer_integration(
        self,
        vulnerability_service,
        pentester_service,
        project_service
    ):
        """Test integration between service layers."""
        
        # Create a pentester
        pentester_data = {
            "email": "<EMAIL>",
            "full_name": "Test Pentester",
            "role": "senior_pentester",
            "skills": ["web_app", "network", "mobile"],
            "certifications": ["OSCP", "CEH"],
        }
        
        pentester = await pentester_service.create(pentester_data)
        assert pentester.id is not None
        
        # Create a project
        project_data = {
            "name": "Integration Test Project",
            "description": "Project for integration testing",
            "client_name": "Test Client",
            "project_type": "web_application",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
        }
        
        project = await project_service.create(project_data)
        assert project.id is not None
        
        # Assign pentester to project
        assignment = await project_service.assign_pentester(
            project.id, pentester.id, "lead_pentester"
        )
        assert assignment is not None
        
        # Create vulnerability for the project
        vulnerability_data = {
            "title": "SQL Injection in Login Form",
            "description": "SQL injection vulnerability found in login form",
            "severity": "HIGH",
            "cvss_score": 8.5,
            "project_id": project.id,
            "discovered_by": pentester.id,
        }
        
        vulnerability = await vulnerability_service.create(vulnerability_data)
        assert vulnerability.id is not None
        assert vulnerability.project_id == project.id
        
        # Verify relationships
        project_vulns = await vulnerability_service.get_by_project(project.id)
        assert len(project_vulns) == 1
        assert project_vulns[0].id == vulnerability.id
        
        pentester_vulns = await vulnerability_service.get_by_discoverer(pentester.id)
        assert len(pentester_vulns) == 1
        assert pentester_vulns[0].id == vulnerability.id
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_vulnerability_correlation_workflow(
        self,
        vulnerability_service,
        test_environment
    ):
        """Test vulnerability correlation using Neo4j graph database."""
        
        # Create multiple related vulnerabilities
        vulnerabilities = [
            {
                "title": "SQL Injection in User Login",
                "description": "SQL injection in login endpoint",
                "severity": "HIGH",
                "cvss_score": 8.5,
                "cve_id": "CVE-2024-0001",
                "mitre_attack_techniques": ["T1190", "T1078"],
            },
            {
                "title": "Privilege Escalation via SQL Injection",
                "description": "Privilege escalation through SQL injection",
                "severity": "CRITICAL",
                "cvss_score": 9.2,
                "cve_id": "CVE-2024-0002",
                "mitre_attack_techniques": ["T1068", "T1078"],
            },
            {
                "title": "Data Exfiltration via Database Access",
                "description": "Sensitive data accessible through database",
                "severity": "HIGH",
                "cvss_score": 7.8,
                "cve_id": "CVE-2024-0003",
                "mitre_attack_techniques": ["T1005", "T1041"],
            },
        ]
        
        created_vulns = []
        for vuln_data in vulnerabilities:
            vuln = await vulnerability_service.create(vuln_data)
            created_vulns.append(vuln)
        
        # Test vulnerability correlation
        correlations = await vulnerability_service.find_correlations(
            created_vulns[0].id
        )
        
        # Should find correlations based on MITRE techniques
        assert len(correlations) > 0
        
        # Verify correlation quality
        for correlation in correlations:
            assert correlation.confidence_score > 0.5
            assert len(correlation.common_techniques) > 0
    
    @pytest.mark.integration
    @pytest.mark.performance
    async def test_high_load_vulnerability_processing(
        self,
        vulnerability_service,
        test_environment
    ):
        """Test system performance under high vulnerability processing load."""
        import time
        
        # Create multiple vulnerabilities concurrently
        vulnerability_count = 100
        start_time = time.time()
        
        tasks = []
        for i in range(vulnerability_count):
            vuln_data = {
                "title": f"Test Vulnerability {i}",
                "description": f"Test vulnerability number {i}",
                "severity": "MEDIUM",
                "cvss_score": 5.0 + (i % 5),
                "cve_id": f"CVE-2024-{i:04d}",
            }
            task = vulnerability_service.create(vuln_data)
            tasks.append(task)
        
        # Execute all tasks concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Verify all vulnerabilities were created successfully
        successful_creates = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_creates) == vulnerability_count
        
        # Performance assertion: should process 100 vulnerabilities in under 30 seconds
        assert processing_time < 30.0, f"Processing took {processing_time:.2f}s, expected < 30s"
        
        # Verify database consistency
        all_vulns = await vulnerability_service.get_all()
        assert len(all_vulns) >= vulnerability_count
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_data_consistency_across_services(
        self,
        vulnerability_service,
        pentester_service,
        project_service,
        test_environment
    ):
        """Test data consistency across multiple services and databases."""
        
        # Create test data
        pentester = await pentester_service.create({
            "email": "<EMAIL>",
            "full_name": "Consistency Test User",
            "role": "pentester",
        })
        
        project = await project_service.create({
            "name": "Consistency Test Project",
            "description": "Testing data consistency",
            "client_name": "Test Client",
        })
        
        vulnerability = await vulnerability_service.create({
            "title": "Consistency Test Vulnerability",
            "description": "Testing data consistency",
            "severity": "HIGH",
            "project_id": project.id,
            "discovered_by": pentester.id,
        })
        
        # Verify data consistency across services
        
        # Check project-vulnerability relationship
        project_vulns = await vulnerability_service.get_by_project(project.id)
        assert any(v.id == vulnerability.id for v in project_vulns)
        
        # Check pentester-vulnerability relationship
        pentester_vulns = await vulnerability_service.get_by_discoverer(pentester.id)
        assert any(v.id == vulnerability.id for v in pentester_vulns)
        
        # Check project-pentester relationship through assignments
        project_assignments = await project_service.get_assignments(project.id)
        # This would depend on whether we created an assignment
        
        # Test cascading updates
        updated_project = await project_service.update(project.id, {
            "name": "Updated Consistency Test Project"
        })
        
        # Verify vulnerability still references correct project
        updated_vulnerability = await vulnerability_service.get_by_id(vulnerability.id)
        assert updated_vulnerability.project_id == project.id
        
        # Test cascading deletes (if implemented)
        # This would depend on the specific cascade rules in the application
