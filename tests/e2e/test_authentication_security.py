"""End-to-end security tests for authentication flows."""

import asyncio
import pytest
from playwright.async_api import async_playwright, <PERSON>, BrowserContext
from playwright.sync_api import expect

from playwright.config import config


@pytest.fixture(scope="session")
async def browser():
    """Create browser instance for testing."""
    async with async_playwright() as p:
        browser = await p.chromium.launch(**config.get_browser_config("chromium"))
        yield browser
        await browser.close()


@pytest.fixture
async def context(browser):
    """Create browser context for testing."""
    context = await browser.new_context(**config.get_context_config())
    yield context
    await context.close()


@pytest.fixture
async def page(context):
    """Create page for testing."""
    page = await context.new_page()
    # Configure page settings
    page.set_default_timeout(config.timeout)
    page.set_default_navigation_timeout(config.timeout * 2)
    yield page
    await page.close()


class TestAuthenticationSecurity:
    """Test suite for authentication security."""
    
    @pytest.mark.e2e
    @pytest.mark.security
    async def test_login_form_security_headers(self, page: Page):
        """Test that login page includes required security headers."""
        response = await page.goto(f"{config.base_url}/login")
        
        # Verify response status
        assert response.status == 200
        
        # Check security headers
        headers = response.headers
        
        # Content Security Policy
        assert "content-security-policy" in headers
        csp = headers["content-security-policy"]
        assert "default-src 'self'" in csp
        assert "script-src" in csp
        assert "style-src" in csp
        
        # X-Frame-Options (clickjacking protection)
        assert "x-frame-options" in headers
        assert headers["x-frame-options"].upper() in ["DENY", "SAMEORIGIN"]
        
        # X-Content-Type-Options
        assert "x-content-type-options" in headers
        assert headers["x-content-type-options"] == "nosniff"
        
        # X-XSS-Protection
        assert "x-xss-protection" in headers
        assert headers["x-xss-protection"] in ["1; mode=block", "0"]
    
    @pytest.mark.e2e
    @pytest.mark.security
    async def test_login_xss_protection(self, page: Page):
        """Test XSS protection in login form."""
        await page.goto(f"{config.base_url}/login")
        
        # Test XSS payloads in username field
        for payload in config.security_tests["xss_payloads"]:
            await page.fill('input[name="username"]', payload)
            await page.fill('input[name="password"]', "testpassword")
            await page.click('button[type="submit"]')
            
            # Verify XSS payload is not executed
            # Check that no alert dialogs appear
            try:
                await page.wait_for_event("dialog", timeout=1000)
                pytest.fail(f"XSS payload executed: {payload}")
            except:
                pass  # Expected - no dialog should appear
            
            # Verify payload is properly escaped in DOM
            username_value = await page.input_value('input[name="username"]')
            if "<script>" in payload:
                assert "<script>" not in await page.content()
            
            # Clear form for next test
            await page.fill('input[name="username"]', "")
    
    @pytest.mark.e2e
    @pytest.mark.security
    async def test_sql_injection_protection(self, page: Page):
        """Test SQL injection protection in login form."""
        await page.goto(f"{config.base_url}/login")
        
        # Test SQL injection payloads
        for payload in config.security_tests["sql_injection_payloads"]:
            await page.fill('input[name="username"]', payload)
            await page.fill('input[name="password"]', "testpassword")
            await page.click('button[type="submit"]')
            
            # Wait for response
            await page.wait_for_timeout(1000)
            
            # Verify we're not logged in (SQL injection failed)
            current_url = page.url
            assert "/dashboard" not in current_url
            assert "/login" in current_url or "/auth" in current_url
            
            # Check for error message (should be generic)
            error_elements = await page.query_selector_all(".error, .alert-danger, [role='alert']")
            if error_elements:
                error_text = await error_elements[0].text_content()
                # Error should be generic, not revealing SQL details
                assert "sql" not in error_text.lower()
                assert "database" not in error_text.lower()
                assert "syntax" not in error_text.lower()
    
    @pytest.mark.e2e
    @pytest.mark.security
    async def test_session_management(self, page: Page):
        """Test session management security."""
        # Login with valid credentials
        await page.goto(f"{config.base_url}/login")
        await page.fill('input[name="username"]', config.test_users["admin"]["username"])
        await page.fill('input[name="password"]', config.test_users["admin"]["password"])
        await page.click('button[type="submit"]')
        
        # Verify successful login
        await page.wait_for_url("**/dashboard")
        
        # Check session cookie properties
        cookies = await page.context.cookies()
        session_cookie = next((c for c in cookies if "session" in c["name"].lower()), None)
        
        if session_cookie:
            # Verify secure cookie attributes
            assert session_cookie.get("secure", False), "Session cookie should be secure"
            assert session_cookie.get("httpOnly", False), "Session cookie should be httpOnly"
            assert session_cookie.get("sameSite") in ["Strict", "Lax"], "Session cookie should have sameSite"
        
        # Test session timeout
        # Navigate to a protected page
        await page.goto(f"{config.base_url}/api/v1/pentesters")
        
        # Simulate session expiry by clearing cookies
        await page.context.clear_cookies()
        
        # Try to access protected resource
        response = await page.goto(f"{config.base_url}/api/v1/pentesters")
        
        # Should be redirected to login or receive 401
        assert response.status in [401, 403] or "/login" in page.url
    
    @pytest.mark.e2e
    @pytest.mark.security
    async def test_csrf_protection(self, page: Page):
        """Test CSRF protection on forms."""
        # Login first
        await page.goto(f"{config.base_url}/login")
        await page.fill('input[name="username"]', config.test_users["admin"]["username"])
        await page.fill('input[name="password"]', config.test_users["admin"]["password"])
        await page.click('button[type="submit"]')
        await page.wait_for_url("**/dashboard")
        
        # Navigate to a form page (e.g., create project)
        await page.goto(f"{config.base_url}/projects/create")
        
        # Check for CSRF token in form
        csrf_token_input = await page.query_selector('input[name="csrf_token"], input[name="_token"]')
        if csrf_token_input:
            csrf_value = await csrf_token_input.get_attribute("value")
            assert csrf_value is not None and len(csrf_value) > 10
        
        # Test form submission without CSRF token
        # Remove CSRF token if present
        if csrf_token_input:
            await page.evaluate("document.querySelector('input[name=\"csrf_token\"], input[name=\"_token\"]').remove()")
        
        # Fill and submit form
        await page.fill('input[name="name"]', "Test Project")
        await page.fill('textarea[name="description"]', "Test Description")
        
        # Submit form and expect failure
        await page.click('button[type="submit"]')
        
        # Should show error or redirect back
        await page.wait_for_timeout(2000)
        
        # Check for CSRF error (implementation dependent)
        error_elements = await page.query_selector_all(".error, .alert-danger, [role='alert']")
        if error_elements:
            error_text = await error_elements[0].text_content()
            # Should indicate invalid request or CSRF error
            assert any(keyword in error_text.lower() for keyword in ["invalid", "forbidden", "csrf", "token"])
    
    @pytest.mark.e2e
    @pytest.mark.security
    async def test_password_policy_enforcement(self, page: Page):
        """Test password policy enforcement."""
        await page.goto(f"{config.base_url}/register")
        
        # Test weak passwords
        weak_passwords = [
            "123456",
            "password",
            "abc123",
            "qwerty",
            "admin",
        ]
        
        for weak_password in weak_passwords:
            await page.fill('input[name="username"]', "<EMAIL>")
            await page.fill('input[name="password"]', weak_password)
            await page.fill('input[name="password_confirm"]', weak_password)
            await page.click('button[type="submit"]')
            
            # Should show password policy error
            await page.wait_for_timeout(1000)
            
            # Check for password policy error message
            error_elements = await page.query_selector_all(".error, .alert-danger, [role='alert']")
            if error_elements:
                error_text = await error_elements[0].text_content()
                assert any(keyword in error_text.lower() for keyword in 
                          ["password", "weak", "policy", "requirements", "strength"])
            
            # Clear form
            await page.fill('input[name="password"]', "")
            await page.fill('input[name="password_confirm"]', "")
    
    @pytest.mark.e2e
    @pytest.mark.security
    async def test_account_lockout_protection(self, page: Page):
        """Test account lockout after multiple failed attempts."""
        await page.goto(f"{config.base_url}/login")
        
        # Attempt multiple failed logins
        for attempt in range(6):  # Assuming 5 attempts trigger lockout
            await page.fill('input[name="username"]', config.test_users["admin"]["username"])
            await page.fill('input[name="password"]', "wrongpassword")
            await page.click('button[type="submit"]')
            
            await page.wait_for_timeout(1000)
            
            # After 5th attempt, should see lockout message
            if attempt >= 4:
                error_elements = await page.query_selector_all(".error, .alert-danger, [role='alert']")
                if error_elements:
                    error_text = await error_elements[0].text_content()
                    if any(keyword in error_text.lower() for keyword in 
                          ["locked", "blocked", "too many", "attempts"]):
                        break
        
        # Try with correct password - should still be locked
        await page.fill('input[name="password"]', config.test_users["admin"]["password"])
        await page.click('button[type="submit"]')
        
        await page.wait_for_timeout(1000)
        
        # Should still show lockout or not redirect to dashboard
        current_url = page.url
        assert "/dashboard" not in current_url
    
    @pytest.mark.e2e
    @pytest.mark.security
    async def test_secure_logout(self, page: Page):
        """Test secure logout functionality."""
        # Login
        await page.goto(f"{config.base_url}/login")
        await page.fill('input[name="username"]', config.test_users["admin"]["username"])
        await page.fill('input[name="password"]', config.test_users["admin"]["password"])
        await page.click('button[type="submit"]')
        await page.wait_for_url("**/dashboard")
        
        # Logout
        await page.click('a[href="/logout"], button[data-action="logout"]')
        
        # Verify redirect to login page
        await page.wait_for_url("**/login")
        
        # Verify session is cleared
        cookies = await page.context.cookies()
        session_cookies = [c for c in cookies if "session" in c["name"].lower()]
        
        # Session cookies should be cleared or expired
        for cookie in session_cookies:
            if "expires" in cookie:
                # Cookie should be expired
                assert cookie["expires"] < 0
        
        # Try to access protected resource
        response = await page.goto(f"{config.base_url}/dashboard")
        
        # Should be redirected to login
        assert "/login" in page.url or response.status in [401, 403]
