version: '3.8'

services:
  # Traefik Reverse Proxy
  traefik:
    image: traefik:v3.0
    container_name: pitas-traefik
    command:
      # Enable Docker provider
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --providers.docker.network=traefik_network
      
      # Configure entrypoints
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      
      # Enable API and dashboard
      - --api.dashboard=true
      - --api.insecure=true
      
      # Configure logging
      - --log.level=INFO
      - --accesslog=true
      
      # Enable metrics
      - --metrics.prometheus=true
      - --metrics.prometheus.addEntryPointsLabels=true
      - --metrics.prometheus.addServicesLabels=true
      
      # Configure certificates (for development)
      - --certificatesresolvers.letsencrypt.acme.email=<EMAIL>
      - --certificatesresolvers.letsencrypt.acme.storage=/acme.json
      - --certificatesresolvers.letsencrypt.acme.httpchallenge=true
      - --certificatesresolvers.letsencrypt.acme.httpchallenge.entrypoint=web
    ports:
      - "8081:80"     # HTTP
      - "8443:443"   # HTTPS
      - "8082:8080" # Traefik Dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik_acme:/acme.json
    networks:
      - traefik_network
      - pitas_net
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik.rule=Host(`traefik.pitas.localhost`)"
      - "traefik.http.routers.traefik.entrypoints=web"
      - "traefik.http.services.traefik.loadbalancer.server.port=8080"

  # PITAS Main Application
  pitas-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: pitas-app
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=****************************************************/pitas_db
      - REDIS_URL=redis://:pitas_redis@redis:6379/0
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_PASSWORD=pitas_neo4j
      - INFLUXDB_URL=http://influxdb:8086
      - INFLUXDB_TOKEN=pitas_influxdb_token
      - TRAEFIK_NETWORK=traefik_network
      - PITAS_NETWORK=pitas_net
      - SERVICE_DOMAIN=pitas.localhost
    volumes:
      - ./src:/app/src
      - ./logs:/app/logs
    networks:
      - traefik_network
      - pitas_net
    depends_on:
      - postgres
      - redis
      - neo4j
      - influxdb
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.pitas-app.rule=Host(`api.pitas.localhost`)"
      - "traefik.http.routers.pitas-app.entrypoints=web"
      - "traefik.http.services.pitas-app.loadbalancer.server.port=8000"
      - "traefik.docker.network=traefik_network"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: pitas-postgres
    environment:
      POSTGRES_DB: pitas_db
      POSTGRES_USER: pitas_user
      POSTGRES_PASSWORD: pitas_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - pitas_net
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.tcp.routers.postgres.rule=HostSNI(`postgres.pitas.localhost`)"
      - "traefik.tcp.routers.postgres.entrypoints=postgres"
      - "traefik.tcp.services.postgres.loadbalancer.server.port=5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U pitas_user -d pitas_db"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: pitas-redis
    command: redis-server --appendonly yes --requirepass pitas_redis
    volumes:
      - redis_data:/data
    networks:
      - pitas_net
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.tcp.routers.redis.rule=HostSNI(`redis.pitas.localhost`)"
      - "traefik.tcp.routers.redis.entrypoints=redis"
      - "traefik.tcp.services.redis.loadbalancer.server.port=6379"
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "pitas_redis", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Neo4j Graph Database
  neo4j:
    image: neo4j:5.14-community
    container_name: pitas-neo4j
    environment:
      - NEO4J_AUTH=neo4j/pitas_neo4j
      - NEO4J_PLUGINS=["apoc", "graph-data-science"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*,gds.*
      - NEO4J_dbms_security_procedures_allowlist=apoc.*,gds.*
      - NEO4J_dbms_memory_heap_initial__size=512m
      - NEO4J_dbms_memory_heap_max__size=2G
      - NEO4J_dbms_memory_pagecache_size=1G
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
    networks:
      - pitas_net
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.neo4j.rule=Host(`neo4j.pitas.localhost`)"
      - "traefik.http.routers.neo4j.entrypoints=web"
      - "traefik.http.services.neo4j.loadbalancer.server.port=7474"
      - "traefik.tcp.routers.neo4j-bolt.rule=HostSNI(`neo4j-bolt.pitas.localhost`)"
      - "traefik.tcp.routers.neo4j-bolt.entrypoints=neo4j-bolt"
      - "traefik.tcp.services.neo4j-bolt.loadbalancer.server.port=7687"
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "pitas_neo4j", "RETURN 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # InfluxDB Time Series Database
  influxdb:
    image: influxdb:2.7-alpine
    container_name: pitas-influxdb
    environment:
      - DOCKER_INFLUXDB_INIT_MODE=setup
      - DOCKER_INFLUXDB_INIT_USERNAME=pitas_admin
      - DOCKER_INFLUXDB_INIT_PASSWORD=pitas_influxdb
      - DOCKER_INFLUXDB_INIT_ORG=pitas
      - DOCKER_INFLUXDB_INIT_BUCKET=security_metrics
      - DOCKER_INFLUXDB_INIT_ADMIN_TOKEN=pitas_influxdb_token
    volumes:
      - influxdb_data:/var/lib/influxdb2
      - influxdb_config:/etc/influxdb2
    networks:
      - pitas_net
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.influxdb.rule=Host(`influxdb.pitas.localhost`)"
      - "traefik.http.routers.influxdb.entrypoints=web"
      - "traefik.http.services.influxdb.loadbalancer.server.port=8086"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8086/health"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: pitas-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=pitas_grafana
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SERVER_DOMAIN=grafana.pitas.localhost
      - GF_SERVER_ROOT_URL=http://grafana.pitas.localhost
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - traefik_network
      - pitas_net
    depends_on:
      - influxdb
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.grafana.rule=Host(`grafana.pitas.localhost`)"
      - "traefik.http.routers.grafana.entrypoints=web"
      - "traefik.http.services.grafana.loadbalancer.server.port=3000"

  # Prometheus Metrics
  prometheus:
    image: prom/prometheus:latest
    container_name: pitas-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--web.external-url=http://prometheus.pitas.localhost'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - traefik_network
      - pitas_net
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.prometheus.rule=Host(`prometheus.pitas.localhost`)"
      - "traefik.http.routers.prometheus.entrypoints=web"
      - "traefik.http.services.prometheus.loadbalancer.server.port=9090"

volumes:
  traefik_acme:
    driver: local
  postgres_data:
    driver: local
  redis_data:
    driver: local
  neo4j_data:
    driver: local
  neo4j_logs:
    driver: local
  neo4j_import:
    driver: local
  neo4j_plugins:
    driver: local
  influxdb_data:
    driver: local
  influxdb_config:
    driver: local
  grafana_data:
    driver: local
  prometheus_data:
    driver: local

networks:
  traefik_network:
    external: true
    name: traefik_network
  pitas_net:
    driver: bridge
    name: pitas_net
