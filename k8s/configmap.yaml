apiVersion: v1
kind: ConfigMap
metadata:
  name: pitas-config
  namespace: pitas
  labels:
    app: pitas
    component: config
data:
  # Application settings
  ENVIRONMENT: "production"
  LOG_LEVEL: "INFO"
  
  # Database settings
  DATABASE_HOST: "postgres-service"
  DATABASE_PORT: "5432"
  DATABASE_NAME: "pitas_prod"
  
  # Redis settings
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  REDIS_DB: "0"
  
  # Security settings
  CORS_ORIGINS: '["https://pitas.yourdomain.com"]'
  ALLOWED_HOSTS: '["pitas.yourdomain.com"]'
  SECURE_SSL_REDIRECT: "true"
  SESSION_COOKIE_SECURE: "true"
  CSRF_COOKIE_SECURE: "true"
  
  # Performance settings
  GUNICORN_WORKERS: "4"
  AVAILABLE_MEMORY_GB: "8"
  
  # Integration settings
  SSO_ENABLED: "true"
  VULNERABILITY_SYNC_ENABLED: "true"
  
  # Monitoring settings
  METRICS_ENABLED: "true"
  TRACING_ENABLED: "true"
  
  # Feature flags
  ADVANCED_ANALYTICS: "true"
  COMPLIANCE_REPORTING: "true"
  TRAINING_MODULE: "true"
