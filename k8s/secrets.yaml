# NOTE: This is a template. Replace with actual base64-encoded values
apiVersion: v1
kind: Secret
metadata:
  name: pitas-secrets
  namespace: pitas
  labels:
    app: pitas
    component: secrets
type: Opaque
data:
  # Base64 encoded values - replace with actual secrets
  SECRET_KEY: "eW91ci1zdXBlci1zZWN1cmUtc2VjcmV0LWtleS1oZXJl"  # your-super-secure-secret-key-here
  DATABASE_PASSWORD: "c2VjdXJlX3Bhc3N3b3Jk"  # secure_password
  REDIS_PASSWORD: "cmVkaXNfcGFzc3dvcmQ="  # redis_password
  
  # SSO secrets
  SSO_SAML_PRIVATE_KEY: "LS0tLS1CRUdJTi4uLi0tLS0t"  # SAML private key
  SSO_OIDC_CLIENT_SECRET: "b2lkY19jbGllbnRfc2VjcmV0"  # OIDC client secret
  LDAP_BIND_PASSWORD: "bGRhcF9iaW5kX3Bhc3N3b3Jk"  # LDAP bind password
  
  # Integration secrets
  NESSUS_API_KEY: "bmVzc3VzX2FwaV9rZXk="  # nessus_api_key
  NESSUS_SECRET_KEY: "bmVzc3VzX3NlY3JldF9rZXk="  # nessus_secret_key
  QUALYS_USERNAME: "cXVhbHlzX3VzZXJuYW1l"  # qualys_username
  QUALYS_PASSWORD: "cXVhbHlzX3Bhc3N3b3Jk"  # qualys_password
  
  # Admin user
  ADMIN_EMAIL: "************************"  # <EMAIL>
  ADMIN_PASSWORD: "YWRtaW4xMjM="  # admin123
  
  # Monitoring
  GRAFANA_ADMIN_PASSWORD: "Z3JhZmFuYV9hZG1pbg=="  # grafana_admin
  
---
# TLS Secret for HTTPS
apiVersion: v1
kind: Secret
metadata:
  name: pitas-tls
  namespace: pitas
  labels:
    app: pitas
    component: tls
type: kubernetes.io/tls
data:
  # Replace with actual certificate and key
  tls.crt: LS0tLS1CRUdJTi4uLi0tLS0t  # Base64 encoded certificate
  tls.key: LS0tLS1CRUdJTi4uLi0tLS0t  # Base64 encoded private key
