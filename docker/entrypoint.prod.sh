#!/bin/bash
set -e

# Production entrypoint script for PITAS

echo "🚀 Starting PITAS Production Deployment..."

# Function to wait for database
wait_for_db() {
    echo "⏳ Waiting for database to be ready..."
    
    while ! python -c "
import psycopg2
import os
import sys
try:
    conn = psycopg2.connect(os.environ['DATABASE_URL'])
    conn.close()
    print('✅ Database is ready!')
    sys.exit(0)
except Exception as e:
    print(f'❌ Database not ready: {e}')
    sys.exit(1)
" 2>/dev/null; do
        echo "⏳ Database not ready, waiting 5 seconds..."
        sleep 5
    done
}

# Function to wait for Redis
wait_for_redis() {
    echo "⏳ Waiting for Redis to be ready..."
    
    while ! python -c "
import redis
import os
import sys
try:
    r = redis.from_url(os.environ['REDIS_URL'])
    r.ping()
    print('✅ Redis is ready!')
    sys.exit(0)
except Exception as e:
    print(f'❌ Redis not ready: {e}')
    sys.exit(1)
" 2>/dev/null; do
        echo "⏳ Redis not ready, waiting 5 seconds..."
        sleep 5
    done
}

# Function to run database migrations
run_migrations() {
    echo "🗄️ Running database migrations..."
    
    # Check if alembic is available
    if command -v alembic &> /dev/null; then
        # Run migrations
        alembic upgrade head
        echo "✅ Database migrations completed!"
    else
        echo "⚠️ Alembic not found, skipping migrations"
    fi
}

# Function to create initial data
create_initial_data() {
    echo "📊 Creating initial data..."
    
    python -c "
import asyncio
import os
from src.pitas.db.session import get_db
from src.pitas.services.user import UserService
from src.pitas.schemas.user import UserCreate

async def create_admin_user():
    admin_email = os.environ.get('ADMIN_EMAIL', '<EMAIL>')
    admin_password = os.environ.get('ADMIN_PASSWORD', 'admin123')
    
    try:
        async for db in get_db():
            user_service = UserService()
            
            # Check if admin user exists
            existing_user = await user_service.get_by_email(db, email=admin_email)
            if not existing_user:
                admin_user = UserCreate(
                    email=admin_email,
                    password=admin_password,
                    full_name='System Administrator',
                    is_superuser=True,
                    is_active=True
                )
                await user_service.create(db, obj_in=admin_user)
                print(f'✅ Admin user created: {admin_email}')
            else:
                print(f'ℹ️ Admin user already exists: {admin_email}')
            break
    except Exception as e:
        print(f'⚠️ Could not create admin user: {e}')

if __name__ == '__main__':
    asyncio.run(create_admin_user())
" || echo "⚠️ Could not create initial data"
}

# Function to validate environment
validate_environment() {
    echo "🔍 Validating environment..."
    
    required_vars=(
        "DATABASE_URL"
        "REDIS_URL"
        "SECRET_KEY"
    )
    
    missing_vars=()
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        echo "❌ Missing required environment variables:"
        printf '   - %s\n' "${missing_vars[@]}"
        exit 1
    fi
    
    echo "✅ Environment validation passed!"
}

# Function to setup logging
setup_logging() {
    echo "📝 Setting up logging..."
    
    # Create log directory if it doesn't exist
    mkdir -p /app/logs
    
    # Set log level based on environment
    export LOG_LEVEL="${LOG_LEVEL:-INFO}"
    
    echo "✅ Logging configured (level: $LOG_LEVEL)"
}

# Function to check system health
health_check() {
    echo "🏥 Performing health check..."
    
    # Check if the application can start
    python -c "
from src.pitas.main import app
print('✅ Application can be imported successfully')
" || {
        echo "❌ Application health check failed"
        exit 1
    }
    
    echo "✅ Health check passed!"
}

# Main execution
main() {
    echo "🎯 PITAS Production Startup Sequence"
    echo "=================================="
    
    # Validate environment
    validate_environment
    
    # Setup logging
    setup_logging
    
    # Wait for dependencies
    wait_for_db
    wait_for_redis
    
    # Run migrations (only if SKIP_MIGRATIONS is not set)
    if [[ -z "$SKIP_MIGRATIONS" ]]; then
        run_migrations
    else
        echo "⏭️ Skipping migrations (SKIP_MIGRATIONS is set)"
    fi
    
    # Create initial data (only if SKIP_INITIAL_DATA is not set)
    if [[ -z "$SKIP_INITIAL_DATA" ]]; then
        create_initial_data
    else
        echo "⏭️ Skipping initial data creation (SKIP_INITIAL_DATA is set)"
    fi
    
    # Health check
    health_check
    
    echo "🚀 Starting PITAS application..."
    echo "=================================="
    
    # Execute the main command
    exec "$@"
}

# Handle signals for graceful shutdown
trap 'echo "🛑 Received shutdown signal, stopping gracefully..."; exit 0' SIGTERM SIGINT

# Run main function
main "$@"
