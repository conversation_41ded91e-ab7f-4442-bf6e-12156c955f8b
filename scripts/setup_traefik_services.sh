#!/bin/bash

# PITAS Traefik Services Setup Script
# This script sets up the Traefik network and starts all PITAS services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    print_status "Checking Docker status..."
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_success "Docker is running"
}

# Function to create Traefik network
create_traefik_network() {
    print_status "Creating Traefik network..."
    
    # Check if network already exists
    if docker network ls | grep -q "traefik_network"; then
        print_warning "Traefik network already exists"
    else
        docker network create traefik_network
        print_success "Traefik network created"
    fi
}

# Function to start services
start_services() {
    print_status "Starting PITAS services with Traefik..."
    
    # Start services using docker compose
    docker compose -f docker-compose.traefik.yml up -d
    
    print_success "Services started successfully"
}

# Function to wait for services to be ready
wait_for_services() {
    print_status "Waiting for services to be ready..."
    
    # Wait for Traefik dashboard
    print_status "Waiting for Traefik dashboard..."
    timeout=60
    counter=0
    while ! curl -s http://localhost:8082/api/rawdata > /dev/null; do
        if [ $counter -ge $timeout ]; then
            print_error "Timeout waiting for Traefik dashboard"
            exit 1
        fi
        sleep 2
        counter=$((counter + 2))
        echo -n "."
    done
    echo
    print_success "Traefik dashboard is ready"
    
    # Wait for main application
    print_status "Waiting for PITAS application..."
    counter=0
    while ! curl -s http://api.pitas.localhost:8081/health > /dev/null; do
        if [ $counter -ge $timeout ]; then
            print_error "Timeout waiting for PITAS application"
            exit 1
        fi
        sleep 2
        counter=$((counter + 2))
        echo -n "."
    done
    echo
    print_success "PITAS application is ready"
}

# Function to display service URLs
display_service_urls() {
    print_success "PITAS services are now running!"
    echo
    echo "Service URLs (*.pitas.localhost):"
    echo "=================================="
    echo "🌐 Main API:          http://api.pitas.localhost:8081"
    echo "📚 API Docs:          http://api.pitas.localhost:8081/api/v1/docs"
    echo "🔧 Traefik Dashboard: http://traefik.pitas.localhost:8081"
    echo "📊 Grafana:           http://grafana.pitas.localhost:8081"
    echo "📈 Prometheus:        http://prometheus.pitas.localhost:8081"
    echo "🗄️  Neo4j Browser:     http://neo4j.pitas.localhost:8081"
    echo "📊 InfluxDB:          http://influxdb.pitas.localhost:8081"
    echo
    echo "Service Management API:"
    echo "======================"
    echo "📋 List Services:     http://api.pitas.localhost:8081/api/v1/services/"
    echo "➕ Register Service:  POST http://api.pitas.localhost:8081/api/v1/services/register"
    echo "❌ Remove Service:    DELETE http://api.pitas.localhost:8081/api/v1/services/{service_name}"
    echo "💚 Health Summary:    http://api.pitas.localhost:8081/api/v1/services/health/summary"
    echo
    echo "Note: Make sure to add these domains to your /etc/hosts file:"
    echo "127.0.0.1 api.pitas.localhost traefik.pitas.localhost grafana.pitas.localhost"
    echo "127.0.0.1 prometheus.pitas.localhost neo4j.pitas.localhost influxdb.pitas.localhost"
}

# Function to update /etc/hosts file
update_hosts_file() {
    print_status "Checking /etc/hosts file..."
    
    hosts_entries=(
        "127.0.0.1 api.pitas.localhost"
        "127.0.0.1 traefik.pitas.localhost"
        "127.0.0.1 grafana.pitas.localhost"
        "127.0.0.1 prometheus.pitas.localhost"
        "127.0.0.1 neo4j.pitas.localhost"
        "127.0.0.1 influxdb.pitas.localhost"
    )
    
    missing_entries=()
    for entry in "${hosts_entries[@]}"; do
        if ! grep -q "$entry" /etc/hosts 2>/dev/null; then
            missing_entries+=("$entry")
        fi
    done
    
    if [ ${#missing_entries[@]} -gt 0 ]; then
        print_warning "Some entries are missing from /etc/hosts file"
        echo "Please add the following entries to your /etc/hosts file:"
        for entry in "${missing_entries[@]}"; do
            echo "  $entry"
        done
        echo
        echo "You can run the following command to add them:"
        echo "sudo bash -c 'cat >> /etc/hosts << EOF"
        for entry in "${missing_entries[@]}"; do
            echo "$entry"
        done
        echo "EOF'"
    else
        print_success "All required entries are present in /etc/hosts"
    fi
}

# Function to show service status
show_service_status() {
    print_status "Service Status:"
    echo "==============="
    docker compose -f docker-compose.traefik.yml ps
}

# Function to show logs
show_logs() {
    if [ "$1" = "logs" ]; then
        print_status "Showing service logs..."
        docker compose -f docker-compose.traefik.yml logs -f
    fi
}

# Main execution
main() {
    echo "PITAS Traefik Services Setup"
    echo "============================"
    echo
    
    # Check prerequisites
    check_docker
    
    # Create network
    create_traefik_network
    
    # Start services
    start_services
    
    # Wait for services
    wait_for_services
    
    # Check hosts file
    update_hosts_file
    
    # Show status
    show_service_status
    
    # Display URLs
    display_service_urls
    
    # Show logs if requested
    show_logs "$1"
}

# Handle script arguments
case "${1:-}" in
    "stop")
        print_status "Stopping PITAS services..."
        docker compose -f docker-compose.traefik.yml down
        print_success "Services stopped"
        ;;
    "restart")
        print_status "Restarting PITAS services..."
        docker compose -f docker-compose.traefik.yml down
        sleep 2
        main
        ;;
    "logs")
        main "logs"
        ;;
    "status")
        show_service_status
        ;;
    *)
        main
        ;;
esac
