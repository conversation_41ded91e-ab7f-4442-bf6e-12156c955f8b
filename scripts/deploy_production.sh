#!/bin/bash
set -e

# PITAS Production Deployment Script
# This script deploys the complete PITAS system to production

echo "🚀 PITAS Production Deployment Script"
echo "====================================="

# Configuration
NAMESPACE="pitas"
IMAGE_TAG="${IMAGE_TAG:-v1.0.0}"
REGISTRY="${REGISTRY:-your-registry.com}"
DOMAIN="${DOMAIN:-pitas.yourdomain.com}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed"
        exit 1
    fi
    
    # Check helm (optional)
    if ! command -v helm &> /dev/null; then
        log_warning "helm is not installed (optional)"
    fi
    
    # Check docker
    if ! command -v docker &> /dev/null; then
        log_error "docker is not installed"
        exit 1
    fi
    
    # Check cluster connectivity
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Build and push Docker image
build_and_push_image() {
    log_info "Building and pushing Docker image..."
    
    # Build production image
    docker build -f Dockerfile.prod -t "${REGISTRY}/pitas:${IMAGE_TAG}" .
    
    # Tag as latest
    docker tag "${REGISTRY}/pitas:${IMAGE_TAG}" "${REGISTRY}/pitas:latest"
    
    # Push to registry
    docker push "${REGISTRY}/pitas:${IMAGE_TAG}"
    docker push "${REGISTRY}/pitas:latest"
    
    log_success "Docker image built and pushed: ${REGISTRY}/pitas:${IMAGE_TAG}"
}

# Create namespace
create_namespace() {
    log_info "Creating namespace..."
    
    kubectl apply -f k8s/namespace.yaml
    
    log_success "Namespace created: ${NAMESPACE}"
}

# Deploy secrets
deploy_secrets() {
    log_info "Deploying secrets..."
    
    # Check if secrets file exists
    if [[ ! -f "k8s/secrets.yaml" ]]; then
        log_error "Secrets file not found: k8s/secrets.yaml"
        log_warning "Please create secrets.yaml with actual base64-encoded values"
        exit 1
    fi
    
    kubectl apply -f k8s/secrets.yaml
    
    log_success "Secrets deployed"
}

# Deploy configuration
deploy_config() {
    log_info "Deploying configuration..."
    
    # Update domain in configmap
    sed -i.bak "s/pitas.yourdomain.com/${DOMAIN}/g" k8s/configmap.yaml
    
    kubectl apply -f k8s/configmap.yaml
    
    # Restore original file
    mv k8s/configmap.yaml.bak k8s/configmap.yaml
    
    log_success "Configuration deployed"
}

# Deploy database
deploy_database() {
    log_info "Deploying PostgreSQL database..."
    
    # Check if postgres deployment exists
    if [[ -f "k8s/postgres.yaml" ]]; then
        kubectl apply -f k8s/postgres.yaml
    else
        log_warning "PostgreSQL deployment file not found, assuming external database"
    fi
    
    log_success "Database deployment completed"
}

# Deploy Redis
deploy_redis() {
    log_info "Deploying Redis..."
    
    # Check if redis deployment exists
    if [[ -f "k8s/redis.yaml" ]]; then
        kubectl apply -f k8s/redis.yaml
    else
        log_warning "Redis deployment file not found, assuming external Redis"
    fi
    
    log_success "Redis deployment completed"
}

# Run database migrations
run_migrations() {
    log_info "Running database migrations..."
    
    # Wait for database to be ready
    log_info "Waiting for database to be ready..."
    kubectl wait --for=condition=ready pod -l app=postgres -n ${NAMESPACE} --timeout=300s || true
    
    # Run migrations using a job
    kubectl run migration-job \
        --image="${REGISTRY}/pitas:${IMAGE_TAG}" \
        --rm -i --restart=Never \
        --namespace=${NAMESPACE} \
        --env-from=configmap/pitas-config \
        --env-from=secret/pitas-secrets \
        -- alembic upgrade head
    
    log_success "Database migrations completed"
}

# Deploy application
deploy_application() {
    log_info "Deploying PITAS application..."
    
    # Update image tag in deployment
    sed -i.bak "s|your-registry.com/pitas:v1.0.0|${REGISTRY}/pitas:${IMAGE_TAG}|g" k8s/deployment.yaml
    
    kubectl apply -f k8s/deployment.yaml
    
    # Restore original file
    mv k8s/deployment.yaml.bak k8s/deployment.yaml
    
    log_success "Application deployed"
}

# Deploy services
deploy_services() {
    log_info "Deploying services..."
    
    kubectl apply -f k8s/service.yaml
    
    log_success "Services deployed"
}

# Deploy ingress
deploy_ingress() {
    log_info "Deploying ingress..."
    
    # Update domain in ingress
    sed -i.bak "s/pitas.yourdomain.com/${DOMAIN}/g" k8s/ingress.yaml
    
    kubectl apply -f k8s/ingress.yaml
    
    # Restore original file
    mv k8s/ingress.yaml.bak k8s/ingress.yaml
    
    log_success "Ingress deployed"
}

# Deploy monitoring
deploy_monitoring() {
    log_info "Deploying monitoring stack..."
    
    # Check if monitoring files exist
    if [[ -d "k8s/monitoring" ]]; then
        kubectl apply -f k8s/monitoring/
        log_success "Monitoring stack deployed"
    else
        log_warning "Monitoring files not found, skipping monitoring deployment"
    fi
}

# Wait for deployment
wait_for_deployment() {
    log_info "Waiting for deployment to be ready..."
    
    kubectl wait --for=condition=available deployment/pitas-api -n ${NAMESPACE} --timeout=600s
    
    log_success "Deployment is ready"
}

# Health check
health_check() {
    log_info "Performing health check..."
    
    # Get service URL
    if kubectl get ingress pitas-ingress -n ${NAMESPACE} &> /dev/null; then
        URL="https://${DOMAIN}"
    else
        # Use port-forward for testing
        kubectl port-forward service/pitas-api-service 8080:80 -n ${NAMESPACE} &
        PORT_FORWARD_PID=$!
        sleep 5
        URL="http://localhost:8080"
    fi
    
    # Check health endpoint
    if curl -f "${URL}/health" &> /dev/null; then
        log_success "Health check passed: ${URL}/health"
    else
        log_error "Health check failed: ${URL}/health"
        
        # Kill port-forward if it was started
        if [[ -n "${PORT_FORWARD_PID}" ]]; then
            kill ${PORT_FORWARD_PID} 2>/dev/null || true
        fi
        
        exit 1
    fi
    
    # Kill port-forward if it was started
    if [[ -n "${PORT_FORWARD_PID}" ]]; then
        kill ${PORT_FORWARD_PID} 2>/dev/null || true
    fi
}

# Display deployment info
display_info() {
    log_success "🎉 PITAS deployment completed successfully!"
    echo ""
    echo "📊 Deployment Information:"
    echo "========================="
    echo "Namespace: ${NAMESPACE}"
    echo "Image: ${REGISTRY}/pitas:${IMAGE_TAG}"
    echo "Domain: ${DOMAIN}"
    echo ""
    echo "🔗 Access URLs:"
    echo "==============="
    echo "Application: https://${DOMAIN}"
    echo "API Documentation: https://${DOMAIN}/api/v1/docs"
    echo "Health Check: https://${DOMAIN}/health"
    echo ""
    echo "📋 Useful Commands:"
    echo "==================="
    echo "View pods: kubectl get pods -n ${NAMESPACE}"
    echo "View logs: kubectl logs -f deployment/pitas-api -n ${NAMESPACE}"
    echo "View services: kubectl get services -n ${NAMESPACE}"
    echo "View ingress: kubectl get ingress -n ${NAMESPACE}"
    echo ""
    echo "🔧 Troubleshooting:"
    echo "==================="
    echo "Describe deployment: kubectl describe deployment pitas-api -n ${NAMESPACE}"
    echo "Check events: kubectl get events -n ${NAMESPACE} --sort-by='.lastTimestamp'"
    echo "Port forward: kubectl port-forward service/pitas-api-service 8080:80 -n ${NAMESPACE}"
}

# Cleanup function
cleanup() {
    log_info "Cleaning up..."
    
    # Kill any background processes
    jobs -p | xargs -r kill 2>/dev/null || true
}

# Trap cleanup on exit
trap cleanup EXIT

# Main deployment function
main() {
    log_info "Starting PITAS production deployment..."
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-build)
                SKIP_BUILD=true
                shift
                ;;
            --skip-migrations)
                SKIP_MIGRATIONS=true
                shift
                ;;
            --domain)
                DOMAIN="$2"
                shift 2
                ;;
            --image-tag)
                IMAGE_TAG="$2"
                shift 2
                ;;
            --registry)
                REGISTRY="$2"
                shift 2
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  --skip-build        Skip Docker image build and push"
                echo "  --skip-migrations   Skip database migrations"
                echo "  --domain DOMAIN     Set domain name (default: pitas.yourdomain.com)"
                echo "  --image-tag TAG     Set image tag (default: v1.0.0)"
                echo "  --registry REGISTRY Set registry URL (default: your-registry.com)"
                echo "  --help              Show this help message"
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Execute deployment steps
    check_prerequisites
    
    if [[ "${SKIP_BUILD}" != "true" ]]; then
        build_and_push_image
    fi
    
    create_namespace
    deploy_secrets
    deploy_config
    deploy_database
    deploy_redis
    
    if [[ "${SKIP_MIGRATIONS}" != "true" ]]; then
        run_migrations
    fi
    
    deploy_application
    deploy_services
    deploy_ingress
    deploy_monitoring
    wait_for_deployment
    health_check
    display_info
    
    log_success "🚀 PITAS is now running in production!"
}

# Run main function
main "$@"
