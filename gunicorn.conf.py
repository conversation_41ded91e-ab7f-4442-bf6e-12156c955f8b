"""Production Gunicorn configuration for PITAS."""

import multiprocessing
import os

# Server socket
bind = "0.0.0.0:8000"
backlog = 2048

# Worker processes
workers = int(os.environ.get("GUNICORN_WORKERS", multiprocessing.cpu_count() * 2 + 1))
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100

# Timeout settings
timeout = 30
keepalive = 2
graceful_timeout = 30

# Logging
accesslog = "-"
errorlog = "-"
loglevel = os.environ.get("LOG_LEVEL", "info").lower()
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# Process naming
proc_name = "pitas"

# Server mechanics
daemon = False
pidfile = "/tmp/pitas.pid"
user = None
group = None
tmp_upload_dir = None

# SSL (if needed)
keyfile = os.environ.get("SSL_KEYFILE")
certfile = os.environ.get("SSL_CERTFILE")

# Worker tuning
preload_app = True
worker_tmp_dir = "/dev/shm"

# Security
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190

# Application settings
forwarded_allow_ips = "*"
secure_scheme_headers = {
    "X-FORWARDED-PROTOCOL": "ssl",
    "X-FORWARDED-PROTO": "https",
    "X-FORWARDED-SSL": "on"
}

# Hooks
def on_starting(server):
    """Called just before the master process is initialized."""
    server.log.info("🚀 PITAS server is starting...")

def on_reload(server):
    """Called to recycle workers during a reload via SIGHUP."""
    server.log.info("🔄 PITAS server is reloading...")

def when_ready(server):
    """Called just after the server is started."""
    server.log.info("✅ PITAS server is ready to accept connections")

def worker_int(worker):
    """Called just after a worker exited on SIGINT or SIGQUIT."""
    worker.log.info("🛑 Worker received INT or QUIT signal")

def pre_fork(server, worker):
    """Called just before a worker is forked."""
    server.log.info(f"👷 Worker {worker.pid} is being forked")

def post_fork(server, worker):
    """Called just after a worker has been forked."""
    server.log.info(f"✨ Worker {worker.pid} has been forked")

def post_worker_init(worker):
    """Called just after a worker has initialized the application."""
    worker.log.info(f"🎯 Worker {worker.pid} has initialized")

def worker_abort(worker):
    """Called when a worker received the SIGABRT signal."""
    worker.log.info(f"💥 Worker {worker.pid} received SIGABRT signal")

def pre_exec(server):
    """Called just before a new master process is forked."""
    server.log.info("🔄 Pre-execution hook called")

def pre_request(worker, req):
    """Called just before a worker processes the request."""
    worker.log.debug(f"📥 Processing request: {req.method} {req.path}")

def post_request(worker, req, environ, resp):
    """Called after a worker processes the request."""
    worker.log.debug(f"📤 Request completed: {req.method} {req.path} - {resp.status}")

def child_exit(server, worker):
    """Called just after a worker has been reaped."""
    server.log.info(f"👋 Worker {worker.pid} has exited")

def worker_exit(server, worker):
    """Called just after a worker has been reaped."""
    server.log.info(f"🚪 Worker {worker.pid} is exiting")

def nworkers_changed(server, new_value, old_value):
    """Called just after num_workers has been changed."""
    server.log.info(f"👥 Number of workers changed from {old_value} to {new_value}")

def on_exit(server):
    """Called just before exiting."""
    server.log.info("👋 PITAS server is shutting down...")

# Environment-specific settings
if os.environ.get("ENVIRONMENT") == "development":
    reload = True
    workers = 1
    loglevel = "debug"
elif os.environ.get("ENVIRONMENT") == "production":
    preload_app = True
    worker_tmp_dir = "/dev/shm"
    
    # Production optimizations
    max_requests = 1000
    max_requests_jitter = 100
    
    # Security headers
    secure_scheme_headers.update({
        "X-FORWARDED-PROTOCOL": "ssl",
        "X-FORWARDED-PROTO": "https",
        "X-FORWARDED-SSL": "on"
    })

# Performance tuning based on available memory
available_memory_gb = int(os.environ.get("AVAILABLE_MEMORY_GB", "4"))
if available_memory_gb >= 8:
    workers = min(workers, 8)
    worker_connections = 2000
elif available_memory_gb >= 4:
    workers = min(workers, 4)
    worker_connections = 1000
else:
    workers = min(workers, 2)
    worker_connections = 500

# Custom error pages
def custom_error_page(status_code, message):
    """Generate custom error page."""
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>PITAS - Error {status_code}</title>
        <style>
            body {{ font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }}
            .error {{ color: #d32f2f; }}
            .message {{ color: #666; margin-top: 20px; }}
        </style>
    </head>
    <body>
        <h1 class="error">Error {status_code}</h1>
        <p class="message">{message}</p>
        <p><a href="/">Return to PITAS</a></p>
    </body>
    </html>
    """

# Health check endpoint
def health_check():
    """Simple health check for load balancers."""
    return "OK"
