A/B Testing Framework
=====================

.. currentmodule:: pitas.services.analytics

Overview
--------

The A/B testing framework provides comprehensive capabilities for designing, executing, and analyzing controlled experiments to optimize user experience, feature effectiveness, and system performance. This framework enables data-driven decision making through statistical analysis and systematic testing.

**Key Features:**

* **Experiment management** with statistical analysis and variant testing
* **User assignment** with randomization and traffic allocation control
* **Metric collection** with real-time tracking and analysis
* **Statistical analysis** with confidence intervals and significance testing

Core Components
---------------

Experiment Types
~~~~~~~~~~~~~~~

.. autoclass:: pitas.db.models.analytics.ExperimentType
   :members:
   :undoc-members:

**Supported Types:**

* ``AB_TEST`` - Classic A/B testing with control and treatment groups
* ``MULTIVARIATE`` - Testing multiple variables simultaneously
* ``FEATURE_FLAG`` - Feature rollout and toggle testing
* ``GRADUAL_ROLLOUT`` - Gradual feature deployment with monitoring

Experiment Lifecycle
~~~~~~~~~~~~~~~~~~~~

.. autoclass:: pitas.db.models.analytics.ExperimentStatus
   :members:
   :undoc-members:

**Status Flow:**

1. ``DRAFT`` - Experiment design and configuration
2. ``ACTIVE`` - Running experiment with user assignment
3. ``PAUSED`` - Temporarily stopped for analysis or adjustments
4. ``COMPLETED`` - Finished with results analysis
5. ``CANCELLED`` - Stopped before completion

Metric Types
~~~~~~~~~~~

.. autoclass:: pitas.db.models.analytics.MetricType
   :members:
   :undoc-members:

**Available Metrics:**

* ``CONVERSION_RATE`` - User conversion and goal completion
* ``CLICK_THROUGH_RATE`` - Click-through rates for UI elements
* ``TIME_ON_PAGE`` - User engagement and session duration
* ``TASK_COMPLETION_RATE`` - Task success and completion rates
* ``ERROR_RATE`` - Error frequency and system reliability
* ``USER_SATISFACTION`` - User satisfaction and feedback scores
* ``PERFORMANCE`` - System performance and response times
* ``ENGAGEMENT`` - User engagement and interaction metrics

Experiment Management
--------------------

Creating A/B Tests
~~~~~~~~~~~~~~~~~

.. code-block:: python

   from pitas.schemas.analytics import ABTestExperimentCreate
   from pitas.services.analytics import AnalyticsService

   # Create A/B test experiment
   experiment_data = ABTestExperimentCreate(
       name="Dashboard Layout Optimization",
       description="Test new dashboard layout vs current layout",
       hypothesis="New layout will improve user engagement by 15%",
       experiment_type="AB_TEST",
       control_variant={"layout": "current", "sidebar": "left"},
       test_variants=[{"layout": "new_design", "sidebar": "right"}],
       primary_metric="ENGAGEMENT",
       secondary_metrics=["TIME_ON_PAGE", "CLICK_THROUGH_RATE"],
       success_criteria="15% improvement in engagement metrics",
       confidence_level=95.0,
       minimum_detectable_effect=15.0,
       traffic_allocation=50.0,
       planned_duration_days=14
   )

   service = AnalyticsService(db)
   experiment = await service.create_ab_test(user_id, experiment_data)

**REST API Endpoint:**

.. code-block:: http

   POST /api/v1/analytics/experiments
   Content-Type: application/json

   {
       "name": "Dashboard Layout Optimization",
       "description": "Test new dashboard layout vs current layout",
       "hypothesis": "New layout will improve user engagement by 15%",
       "experiment_type": "AB_TEST",
       "control_variant": {"layout": "current", "sidebar": "left"},
       "test_variants": [{"layout": "new_design", "sidebar": "right"}],
       "primary_metric": "ENGAGEMENT",
       "secondary_metrics": ["TIME_ON_PAGE", "CLICK_THROUGH_RATE"],
       "success_criteria": "15% improvement in engagement metrics",
       "confidence_level": 95.0,
       "minimum_detectable_effect": 15.0,
       "traffic_allocation": 50.0,
       "planned_duration_days": 14
   }

Starting Experiments
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Start experiment
   started_experiment = await service.start_experiment(experiment.id)

**REST API Endpoint:**

.. code-block:: http

   POST /api/v1/analytics/experiments/{experiment_id}/start

User Assignment
--------------

Automatic User Assignment
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Assign user to experiment variant
   participant = await service.assign_user_to_experiment(experiment.id, user_id)

**REST API Endpoint:**

.. code-block:: http

   POST /api/v1/analytics/experiments/{experiment_id}/assign
   Content-Type: application/json

   {
       "user_id": "optional-user-id"
   }

**Response:**

.. code-block:: json

   {
       "id": "participant-uuid",
       "experiment_id": "experiment-uuid",
       "user_id": "user-uuid",
       "variant_assigned": "control",
       "assignment_timestamp": "2024-01-15T10:30:00Z",
       "total_exposures": 0,
       "has_converted": false
   }

Client-Side Integration
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   // JavaScript client integration
   async function getExperimentVariant(experimentId) {
       const response = await fetch(`/api/v1/analytics/experiments/${experimentId}/assign`, {
           method: 'POST',
           headers: {
               'Content-Type': 'application/json',
               'Authorization': `Bearer ${token}`
           }
       });
       
       const participant = await response.json();
       return participant.variant_assigned;
   }

   // Use variant in application
   const variant = await getExperimentVariant('dashboard-layout-test');
   if (variant === 'control') {
       renderCurrentDashboard();
   } else {
       renderNewDashboard();
   }

Metric Collection
----------------

Recording Experiment Metrics
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   from pitas.schemas.analytics import ExperimentMetricCreate

   # Record experiment metric
   metric_data = ExperimentMetricCreate(
       experiment_id=experiment.id,
       participant_id=participant.id,
       metric_type="ENGAGEMENT",
       metric_value=1.0,
       variant=participant.variant_assigned,
       session_id="session_123",
       page_url="/dashboard",
       additional_data={"action": "click", "element": "nav_button"}
   )

   metric = await service.record_experiment_metric(metric_data)

**REST API Endpoint:**

.. code-block:: http

   POST /api/v1/analytics/experiments/metrics
   Content-Type: application/json

   {
       "experiment_id": "experiment-uuid",
       "participant_id": "participant-uuid",
       "metric_type": "ENGAGEMENT",
       "metric_value": 1.0,
       "variant": "control",
       "session_id": "session_123",
       "page_url": "/dashboard",
       "additional_data": {"action": "click", "element": "nav_button"}
   }

Automatic Metric Collection
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   // Automatic metric collection
   class ExperimentTracker {
       constructor(experimentId, participantId, variant) {
           this.experimentId = experimentId;
           this.participantId = participantId;
           this.variant = variant;
       }

       trackEngagement(action, element) {
           this.recordMetric('ENGAGEMENT', 1.0, {
               action: action,
               element: element
           });
       }

       trackConversion(value = 1.0) {
           this.recordMetric('CONVERSION_RATE', value);
       }

       trackTimeOnPage(duration) {
           this.recordMetric('TIME_ON_PAGE', duration);
       }

       async recordMetric(metricType, value, additionalData = {}) {
           await fetch('/api/v1/analytics/experiments/metrics', {
               method: 'POST',
               headers: {
                   'Content-Type': 'application/json',
                   'Authorization': `Bearer ${this.token}`
               },
               body: JSON.stringify({
                   experiment_id: this.experimentId,
                   participant_id: this.participantId,
                   metric_type: metricType,
                   metric_value: value,
                   variant: this.variant,
                   additional_data: additionalData
               })
           });
       }
   }

Results Analysis
---------------

Experiment Results
~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Analyze experiment results
   results = await service.analyze_experiment_results(experiment.id)

**REST API Endpoint:**

.. code-block:: http

   GET /api/v1/analytics/experiments/{experiment_id}/results

**Response:**

.. code-block:: json

   {
       "experiment_id": "experiment-uuid",
       "total_participants": 1000,
       "participants_by_variant": {
           "control": 500,
           "variant_0": 500
       },
       "conversion_rates": {
           "control": 12.5,
           "variant_0": 15.2
       },
       "statistical_significance": true,
       "confidence_interval": {
           "control": [10.8, 14.2],
           "variant_0": [13.1, 17.3]
       },
       "p_value": 0.023,
       "effect_size": 0.027,
       "winning_variant": "variant_0",
       "recommendation": "Implement variant_0"
   }

Statistical Analysis
~~~~~~~~~~~~~~~~~~~

The framework provides comprehensive statistical analysis:

**Sample Size Calculation:**

.. automethod:: AnalyticsService._calculate_sample_size

**Key Statistical Measures:**

* **P-value** - Probability of observing results by chance
* **Confidence Interval** - Range of likely true values
* **Effect Size** - Magnitude of difference between variants
* **Statistical Power** - Probability of detecting true effects

Advanced Features
----------------

Multivariate Testing
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Multivariate test with multiple variables
   multivariate_data = ABTestExperimentCreate(
       name="Dashboard Multivariate Test",
       experiment_type="MULTIVARIATE",
       control_variant={
           "layout": "current",
           "color_scheme": "blue",
           "navigation": "top"
       },
       test_variants=[
           {
               "layout": "new",
               "color_scheme": "blue", 
               "navigation": "top"
           },
           {
               "layout": "current",
               "color_scheme": "green",
               "navigation": "top"
           },
           {
               "layout": "current",
               "color_scheme": "blue",
               "navigation": "side"
           }
       ]
   )

Feature Flags
~~~~~~~~~~~~

.. code-block:: python

   # Feature flag experiment
   feature_flag_data = ABTestExperimentCreate(
       name="New Search Feature",
       experiment_type="FEATURE_FLAG",
       control_variant={"search_enabled": False},
       test_variants=[{"search_enabled": True}],
       traffic_allocation=10.0  # Start with 10% of users
   )

Gradual Rollout
~~~~~~~~~~~~~~

.. code-block:: python

   # Gradual rollout with increasing traffic
   rollout_schedule = [
       {"week": 1, "traffic": 5.0},
       {"week": 2, "traffic": 15.0},
       {"week": 3, "traffic": 35.0},
       {"week": 4, "traffic": 100.0}
   ]

Best Practices
--------------

Experiment Design
~~~~~~~~~~~~~~~~

1. **Clear Hypothesis** - Define specific, testable hypotheses
2. **Single Variable** - Test one variable at a time (for A/B tests)
3. **Sufficient Sample Size** - Calculate required sample size
4. **Random Assignment** - Ensure proper randomization

Statistical Rigor
~~~~~~~~~~~~~~~~~

1. **Pre-define Metrics** - Choose metrics before starting
2. **Avoid Peeking** - Don't stop early based on interim results
3. **Multiple Testing** - Adjust for multiple comparisons
4. **Practical Significance** - Consider business impact, not just statistical significance

Implementation
~~~~~~~~~~~~~

1. **Gradual Rollout** - Start with small traffic percentages
2. **Monitor Closely** - Watch for unexpected issues
3. **Quick Rollback** - Have rollback plans ready
4. **Document Results** - Record learnings and decisions

Configuration
-------------

Environment Variables
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # A/B testing configuration
   AB_TESTING_ENABLED=true
   AB_TESTING_DEFAULT_CONFIDENCE=95.0
   AB_TESTING_MIN_SAMPLE_SIZE=100
   
   # Statistical analysis
   STATISTICAL_SIGNIFICANCE_THRESHOLD=0.05
   MINIMUM_EFFECT_SIZE=0.02

Database Schema
~~~~~~~~~~~~~~

The A/B testing system uses the following tables:

* ``ab_test_experiments`` - Experiment definitions
* ``experiment_participants`` - User assignments
* ``experiment_metrics`` - Metric collection

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~

**Low Statistical Power**
   - Increase sample size
   - Extend experiment duration
   - Choose more sensitive metrics

**Biased Results**
   - Check randomization algorithm
   - Verify user assignment logic
   - Review traffic allocation

**Metric Collection Issues**
   - Verify client-side tracking
   - Check API endpoint connectivity
   - Review metric definition accuracy
