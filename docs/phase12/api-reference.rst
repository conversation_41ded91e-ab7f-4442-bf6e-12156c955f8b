Phase 12 API Reference
======================

.. currentmodule:: pitas.api.v1.endpoints

Overview
--------

Phase 12 provides comprehensive REST API endpoints for continuous improvement and innovation management. The API is organized into three main modules:

* **Feedback API** - User feedback collection, behavior analytics, and satisfaction surveys
* **Innovation API** - Innovation project management, technology trends, and proof-of-concepts
* **Analytics API** - A/B testing framework, performance benchmarks, and improvement recommendations

All endpoints follow RESTful conventions and return JSON responses. Authentication is required for all endpoints using JWT tokens.

Base URL
--------

.. code-block:: text

   https://api.pitas.com/api/v1/

Authentication
--------------

All API endpoints require authentication using JWT tokens:

.. code-block:: http

   Authorization: Bearer <jwt_token>

Feedback API
-----------

.. automodule:: pitas.api.v1.endpoints.feedback
   :members:
   :undoc-members:

Feedback Collection Endpoints
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /feedback/

   Create new user feedback

   **Request Body:**

   .. code-block:: json

      {
          "feedback_type": "FEATURE_REQUEST",
          "title": "Improve dashboard performance",
          "description": "The dashboard loads slowly and needs optimization",
          "rating": 3,
          "nps_score": 7,
          "feature_area": "dashboard",
          "workflow_step": "initial_load",
          "page_url": "/dashboard",
          "session_id": "session_123"
      }

   **Response:**

   .. code-block:: json

      {
          "id": "feedback-uuid",
          "user_id": "user-uuid",
          "feedback_type": "FEATURE_REQUEST",
          "title": "Improve dashboard performance",
          "description": "The dashboard loads slowly and needs optimization",
          "rating": 3,
          "sentiment_score": "NEUTRAL",
          "priority_score": 6.5,
          "status": "SUBMITTED",
          "created_at": "2024-01-15T10:30:00Z"
      }

.. http:get:: /feedback/

   List user feedback with filtering options

   **Query Parameters:**

   * ``skip`` (int) - Number of records to skip (default: 0)
   * ``limit`` (int) - Maximum records to return (default: 100)
   * ``feedback_type`` (str) - Filter by feedback type
   * ``status`` (str) - Filter by status
   * ``assigned_to_me`` (bool) - Show only feedback assigned to current user

.. http:get:: /feedback/(uuid:feedback_id)

   Get specific feedback by ID

.. http:put:: /feedback/(uuid:feedback_id)

   Update feedback (admin only)

.. http:put:: /feedback/(uuid:feedback_id)/status

   Update feedback status and assignment

   **Query Parameters:**

   * ``status`` (str) - New status (required)
   * ``assigned_to`` (uuid) - User to assign to (optional)
   * ``resolution_notes`` (str) - Resolution notes (optional)

Behavior Analytics Endpoints
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /feedback/behavior

   Track user behavior for analytics

   **Request Body:**

   .. code-block:: json

      {
          "session_id": "session_123",
          "event_type": "page_view",
          "event_data": {"page": "dashboard", "action": "load"},
          "page_url": "/dashboard",
          "user_agent": "Mozilla/5.0...",
          "page_load_time": 2.5,
          "time_on_page": 120.0
      }

.. http:post:: /feedback/feature-usage

   Update feature usage metrics

   **Query Parameters:**

   * ``feature_name`` (str) - Name of the feature (required)

   **Request Body:**

   .. code-block:: json

      {
          "usage_count": 1,
          "total_time_spent": 300.0,
          "success_rate": 95.0,
          "error_count": 0,
          "usage_context": {"scan_type": "full"}
      }

Survey Endpoints
~~~~~~~~~~~~~~~

.. http:post:: /feedback/surveys

   Create satisfaction survey response

.. http:get:: /feedback/surveys/

   List satisfaction surveys

Analytics Endpoints
~~~~~~~~~~~~~~~~~~

.. http:get:: /feedback/analytics/overview

   Get feedback analytics overview

   **Query Parameters:**

   * ``start_date`` (datetime) - Start date for analysis
   * ``end_date`` (datetime) - End date for analysis

.. http:get:: /feedback/analytics/engagement

   Get user engagement metrics

.. http:get:: /feedback/summary

   Get comprehensive feedback summary

Innovation API
-------------

.. automodule:: pitas.api.v1.endpoints.innovation
   :members:
   :undoc-members:

Project Management Endpoints
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /innovation/projects

   Create new innovation project

   **Request Body:**

   .. code-block:: json

      {
          "title": "AI-Powered Vulnerability Detection",
          "description": "Implement machine learning for automated vulnerability detection",
          "category": "ARTIFICIAL_INTELLIGENCE",
          "priority": "HIGH",
          "business_value": "Reduce manual effort and improve detection accuracy",
          "expected_roi": 3.5,
          "estimated_cost": 50000.0,
          "estimated_timeline_weeks": 12
      }

.. http:get:: /innovation/projects

   List innovation projects with filtering

   **Query Parameters:**

   * ``skip`` (int) - Number of records to skip
   * ``limit`` (int) - Maximum records to return
   * ``status`` (str) - Filter by project status
   * ``category`` (str) - Filter by technology category
   * ``assigned_to_me`` (bool) - Show only assigned projects

.. http:get:: /innovation/projects/(uuid:project_id)

   Get specific innovation project

.. http:put:: /innovation/projects/(uuid:project_id)

   Update innovation project

.. http:put:: /innovation/projects/(uuid:project_id)/status

   Update project status

   **Query Parameters:**

   * ``status`` (str) - New status (required)
   * ``assigned_to`` (uuid) - User to assign to (optional)

Evaluation Endpoints
~~~~~~~~~~~~~~~~~~~

.. http:post:: /innovation/projects/(uuid:project_id)/evaluations

   Add evaluation to innovation project

   **Request Body:**

   .. code-block:: json

      {
          "technical_feasibility": 8.0,
          "business_impact": 9.0,
          "implementation_effort": 6.0,
          "risk_assessment": 4.0,
          "strategic_alignment": 8.5,
          "recommendation": "approve",
          "strengths": "Strong business case and technical feasibility",
          "recommendations": "Proceed with proof of concept"
      }

.. http:get:: /innovation/projects/(uuid:project_id)/evaluations

   Get evaluations for innovation project

Technology Trend Endpoints
~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /innovation/technology-trends

   Track new technology trend

.. http:get:: /innovation/technology-trends

   List technology trends

   **Query Parameters:**

   * ``category`` (str) - Filter by technology category
   * ``limit`` (int) - Maximum records to return

Proof-of-Concept Endpoints
~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /innovation/proof-of-concepts

   Create proof of concept project

.. http:get:: /innovation/proof-of-concepts

   List proof of concept projects

Pipeline Endpoints
~~~~~~~~~~~~~~~~~

.. http:get:: /innovation/pipeline

   Get innovation pipeline overview

.. http:get:: /innovation/metrics

   Get innovation metrics and KPIs

.. http:get:: /innovation/summary

   Get comprehensive innovation summary

Analytics API
------------

.. automodule:: pitas.api.v1.endpoints.analytics
   :members:
   :undoc-members:

Experiment Management Endpoints
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /analytics/experiments

   Create new A/B test experiment

   **Request Body:**

   .. code-block:: json

      {
          "name": "Dashboard Layout Test",
          "description": "Test new dashboard layout vs current layout",
          "hypothesis": "New layout will improve user engagement by 15%",
          "experiment_type": "AB_TEST",
          "control_variant": {"layout": "current"},
          "test_variants": [{"layout": "new_design"}],
          "primary_metric": "ENGAGEMENT",
          "success_criteria": "15% improvement in engagement metrics",
          "confidence_level": 95.0,
          "minimum_detectable_effect": 15.0
      }

.. http:get:: /analytics/experiments

   List A/B test experiments

.. http:get:: /analytics/experiments/(uuid:experiment_id)

   Get specific A/B test experiment

.. http:put:: /analytics/experiments/(uuid:experiment_id)

   Update A/B test experiment

.. http:post:: /analytics/experiments/(uuid:experiment_id)/start

   Start A/B test experiment

User Assignment Endpoints
~~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /analytics/experiments/(uuid:experiment_id)/assign

   Assign user to experiment variant

.. http:post:: /analytics/experiments/metrics

   Record metric for experiment participant

   **Request Body:**

   .. code-block:: json

      {
          "experiment_id": "experiment-uuid",
          "participant_id": "participant-uuid",
          "metric_type": "ENGAGEMENT",
          "metric_value": 1.0,
          "variant": "control",
          "session_id": "session_123"
      }

Results Analysis Endpoints
~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /analytics/experiments/(uuid:experiment_id)/results

   Get A/B test experiment results

.. http:get:: /analytics/experiments/(uuid:experiment_id)/participants

   Get experiment participants

Performance Monitoring Endpoints
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /analytics/benchmarks

   Create performance benchmark

.. http:get:: /analytics/benchmarks

   List performance benchmarks

.. http:put:: /analytics/benchmarks/(uuid:benchmark_id)

   Update performance benchmark with new measurement

   **Query Parameters:**

   * ``new_value`` (float) - New measurement value (required)

.. http:get:: /analytics/benchmarks/(uuid:benchmark_id)/trends

   Get performance benchmark trends

Improvement Recommendations
~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /analytics/recommendations

   List improvement recommendations

.. http:post:: /analytics/recommendations/generate

   Generate AI-powered improvement recommendations

   **Query Parameters:**

   * ``limit`` (int) - Maximum recommendations to generate

.. http:put:: /analytics/recommendations/(uuid:recommendation_id)

   Update improvement recommendation

Dashboard Endpoints
~~~~~~~~~~~~~~~~~~

.. http:get:: /analytics/dashboard

   Get analytics dashboard data

.. http:get:: /analytics/metrics

   Get continuous improvement KPIs

Error Responses
--------------

All endpoints return standard HTTP status codes and error responses:

**400 Bad Request**

.. code-block:: json

   {
       "detail": "Invalid request data",
       "errors": [
           {
               "field": "rating",
               "message": "Rating must be between 1 and 5"
           }
       ]
   }

**401 Unauthorized**

.. code-block:: json

   {
       "detail": "Authentication required"
   }

**403 Forbidden**

.. code-block:: json

   {
       "detail": "Insufficient permissions"
   }

**404 Not Found**

.. code-block:: json

   {
       "detail": "Resource not found"
   }

**422 Validation Error**

.. code-block:: json

   {
       "detail": [
           {
               "loc": ["body", "feedback_type"],
               "msg": "field required",
               "type": "value_error.missing"
           }
       ]
   }

Rate Limiting
------------

API endpoints are rate limited to prevent abuse:

* **Standard endpoints**: 1000 requests per hour per user
* **Analytics endpoints**: 500 requests per hour per user
* **Bulk operations**: 100 requests per hour per user

Rate limit headers are included in responses:

.. code-block:: http

   X-RateLimit-Limit: 1000
   X-RateLimit-Remaining: 999
   X-RateLimit-Reset: 1642248000

Pagination
----------

List endpoints support pagination using ``skip`` and ``limit`` parameters:

.. code-block:: http

   GET /api/v1/feedback/?skip=20&limit=10

Response includes pagination metadata:

.. code-block:: json

   {
       "items": [...],
       "total": 150,
       "skip": 20,
       "limit": 10,
       "has_more": true
   }

SDK Examples
-----------

Python SDK
~~~~~~~~~~

.. code-block:: python

   from pitas_client import PitasClient

   client = PitasClient(api_key="your-api-key")

   # Create feedback
   feedback = client.feedback.create({
       "feedback_type": "FEATURE_REQUEST",
       "title": "Improve dashboard",
       "description": "Dashboard needs performance optimization"
   })

   # Start A/B test
   experiment = client.analytics.experiments.create({
       "name": "Button Color Test",
       "hypothesis": "Red button will increase clicks",
       "experiment_type": "AB_TEST"
   })
   
   client.analytics.experiments.start(experiment.id)

JavaScript SDK
~~~~~~~~~~~~~

.. code-block:: javascript

   import { PitasClient } from '@pitas/client';

   const client = new PitasClient({ apiKey: 'your-api-key' });

   // Track user behavior
   await client.feedback.trackBehavior({
       sessionId: 'session_123',
       eventType: 'page_view',
       pageUrl: '/dashboard'
   });

   // Get experiment variant
   const participant = await client.analytics.experiments.assign(experimentId);
   const variant = participant.variantAssigned;
