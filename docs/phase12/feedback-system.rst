Feedback Collection and Analysis System
=======================================

.. currentmodule:: pitas.services.feedback

Overview
--------

The feedback collection and analysis system provides comprehensive mechanisms for gathering, analyzing, and acting upon user feedback to drive continuous improvement. This system combines automated collection, intelligent analysis, and actionable insights to create a robust feedback loop.

**Key Features:**

* **Automated sentiment analysis** with real-time feedback processing
* **Behavioral analytics** tracking user interactions and patterns
* **Feature usage metrics** with adoption and effectiveness measurement
* **Satisfaction surveys** including NPS tracking and trend analysis

Core Components
---------------

User Feedback Collection
~~~~~~~~~~~~~~~~~~~~~~~~

The system supports multiple types of feedback collection:

.. autoclass:: pitas.db.models.feedback.FeedbackType
   :members:
   :undoc-members:

**Feedback Types:**

* ``USER_EXPERIENCE`` - General user experience feedback
* ``FEATURE_REQUEST`` - Requests for new features or enhancements
* ``BUG_REPORT`` - Bug reports and issue identification
* ``PERFORMANCE_ISSUE`` - Performance-related concerns
* ``WORKFLOW_IMPROVEMENT`` - Process and workflow optimization suggestions
* ``GENERAL_SATISFACTION`` - Overall satisfaction feedback
* ``NPS_SURVEY`` - Net Promoter Score survey responses

Feedback Processing Pipeline
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph LR
       A[User Feedback] --> B[Sentiment Analysis]
       B --> C[Priority Scoring]
       C --> D[Category Tagging]
       D --> E[Assignment & Routing]
       E --> F[Response & Resolution]
       F --> G[Analytics & Insights]

**Processing Steps:**

1. **Collection** - Feedback captured through various touchpoints
2. **Analysis** - Automated sentiment and priority analysis
3. **Categorization** - Intelligent tagging and classification
4. **Routing** - Assignment to appropriate team members
5. **Response** - Acknowledgment and resolution tracking
6. **Analytics** - Aggregation and insight generation

API Usage Examples
------------------

Creating User Feedback
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   from pitas.schemas.feedback import UserFeedbackCreate
   from pitas.services.feedback import FeedbackService

   # Create feedback
   feedback_data = UserFeedbackCreate(
       feedback_type="FEATURE_REQUEST",
       title="Improve dashboard performance",
       description="The dashboard loads slowly and needs optimization",
       rating=3,
       feature_area="dashboard",
       workflow_step="initial_load"
   )

   service = FeedbackService(db)
   feedback = await service.create_feedback(user_id, feedback_data)

**REST API Endpoint:**

.. code-block:: http

   POST /api/v1/feedback/
   Content-Type: application/json

   {
       "feedback_type": "FEATURE_REQUEST",
       "title": "Improve dashboard performance",
       "description": "The dashboard loads slowly and needs optimization",
       "rating": 3,
       "feature_area": "dashboard",
       "workflow_step": "initial_load"
   }

Tracking User Behavior
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   from pitas.schemas.feedback import UserBehaviorAnalyticsCreate

   # Track user behavior
   analytics_data = UserBehaviorAnalyticsCreate(
       session_id="session_123",
       event_type="page_view",
       event_data={"page": "dashboard", "action": "load"},
       page_url="/dashboard",
       user_agent="Mozilla/5.0...",
       page_load_time=2.5,
       time_on_page=120.0
   )

   analytics = await service.track_user_behavior(user_id, analytics_data)

**REST API Endpoint:**

.. code-block:: http

   POST /api/v1/feedback/behavior
   Content-Type: application/json

   {
       "session_id": "session_123",
       "event_type": "page_view",
       "event_data": {"page": "dashboard", "action": "load"},
       "page_url": "/dashboard",
       "user_agent": "Mozilla/5.0...",
       "page_load_time": 2.5,
       "time_on_page": 120.0
   }

Feature Usage Tracking
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   from pitas.schemas.feedback import FeatureUsageMetricsCreate

   # Update feature usage
   usage_data = FeatureUsageMetricsCreate(
       feature_name="vulnerability_scanner",
       usage_count=1,
       total_time_spent=300.0,
       success_rate=95.0,
       error_count=0,
       usage_context={"scan_type": "full", "target_count": 5}
   )

   metrics = await service.update_feature_usage(user_id, "vulnerability_scanner", usage_data)

Satisfaction Surveys
~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   from pitas.schemas.feedback import SatisfactionSurveyCreate

   # Create satisfaction survey
   survey_data = SatisfactionSurveyCreate(
       survey_type="quarterly_nps",
       nps_score=8,
       satisfaction_rating=4,
       likelihood_to_recommend=4,
       what_works_well="Great vulnerability detection capabilities",
       improvement_suggestions="Improve reporting interface",
       survey_version="2.0"
   )

   survey = await service.create_satisfaction_survey(user_id, survey_data)

Analytics and Insights
----------------------

Feedback Analytics Overview
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Get comprehensive feedback analytics
   analytics = await service.get_feedback_analytics(
       start_date=datetime.utcnow() - timedelta(days=30),
       end_date=datetime.utcnow()
   )

   print(f"Total feedback: {analytics.total_feedback}")
   print(f"Average rating: {analytics.average_rating}")
   print(f"Average NPS: {analytics.average_nps_score}")

**REST API Endpoint:**

.. code-block:: http

   GET /api/v1/feedback/analytics/overview?start_date=2024-01-01&end_date=2024-01-31

**Response:**

.. code-block:: json

   {
       "total_feedback": 150,
       "feedback_by_type": {
           "feature_request": 45,
           "bug_report": 30,
           "user_experience": 75
       },
       "feedback_by_status": {
           "submitted": 20,
           "in_progress": 45,
           "implemented": 85
       },
       "average_rating": 4.2,
       "average_nps_score": 7.8,
       "sentiment_distribution": {
           "positive": 60,
           "neutral": 70,
           "negative": 20
       },
       "resolution_time_avg_hours": 48.5
   }

User Engagement Metrics
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: http

   GET /api/v1/feedback/analytics/engagement

**Response:**

.. code-block:: json

   {
       "total_users": 100,
       "active_users_last_30_days": 85,
       "average_session_duration": 1800.0,
       "page_views_per_session": 12.5,
       "bounce_rate": 25.0,
       "feature_adoption_rates": {
           "dashboard": 95.0,
           "reports": 78.0,
           "analytics": 65.0
       }
   }

Automated Analysis Features
---------------------------

Sentiment Analysis
~~~~~~~~~~~~~~~~~

The system automatically analyzes feedback sentiment using natural language processing:

.. automethod:: FeedbackService._analyze_sentiment

**Sentiment Scores:**

* ``VERY_POSITIVE`` - Highly positive feedback
* ``POSITIVE`` - Generally positive feedback  
* ``NEUTRAL`` - Neutral or mixed feedback
* ``NEGATIVE`` - Generally negative feedback
* ``VERY_NEGATIVE`` - Highly negative feedback

Priority Scoring
~~~~~~~~~~~~~~~~

Automatic priority scoring based on multiple factors:

.. automethod:: FeedbackService._calculate_priority_score

**Scoring Factors:**

* Feedback type (bug reports get higher priority)
* User rating (lower ratings increase priority)
* NPS score (detractors get higher priority)
* Feature area impact assessment
* Historical resolution patterns

Category Tagging
~~~~~~~~~~~~~~~

Intelligent categorization of feedback:

.. automethod:: FeedbackService._extract_category_tags

**Category Examples:**

* ``ui`` - User interface related feedback
* ``performance`` - Performance and speed issues
* ``security`` - Security-related concerns
* ``workflow`` - Process and workflow feedback

Best Practices
--------------

Feedback Collection
~~~~~~~~~~~~~~~~~~

1. **Contextual Collection** - Gather feedback at relevant workflow points
2. **Multiple Channels** - Provide various feedback mechanisms
3. **Timely Requests** - Ask for feedback when experiences are fresh
4. **Clear Purpose** - Explain how feedback will be used

Response Management
~~~~~~~~~~~~~~~~~~

1. **Acknowledge Quickly** - Respond to feedback within 24 hours
2. **Be Transparent** - Share what actions will be taken
3. **Follow Up** - Update users on resolution progress
4. **Close the Loop** - Inform users when issues are resolved

Analytics Usage
~~~~~~~~~~~~~~

1. **Regular Review** - Analyze feedback trends weekly
2. **Action Planning** - Convert insights into improvement plans
3. **Trend Monitoring** - Watch for emerging patterns
4. **Success Measurement** - Track improvement impact

Configuration
-------------

Environment Variables
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Feedback system configuration
   FEEDBACK_SENTIMENT_ANALYSIS_ENABLED=true
   FEEDBACK_AUTO_ASSIGNMENT_ENABLED=true
   FEEDBACK_NOTIFICATION_ENABLED=true
   
   # Analytics configuration
   ANALYTICS_RETENTION_DAYS=365
   ANALYTICS_AGGREGATION_INTERVAL=daily

Database Configuration
~~~~~~~~~~~~~~~~~~~~~

The feedback system uses the following tables:

* ``user_feedback`` - Main feedback records
* ``feedback_responses`` - Responses to feedback
* ``user_behavior_analytics`` - Behavior tracking data
* ``feature_usage_metrics`` - Feature usage statistics
* ``satisfaction_surveys`` - Survey responses

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~

**Sentiment Analysis Not Working**
   - Check if NLP dependencies are installed
   - Verify sentiment analysis is enabled in configuration
   - Review feedback text for processing issues

**Missing Analytics Data**
   - Ensure behavior tracking is properly configured
   - Check database connectivity and permissions
   - Verify analytics aggregation jobs are running

**Slow Feedback Processing**
   - Review database query performance
   - Check for proper indexing on feedback tables
   - Monitor background task processing

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~

1. **Database Indexing** - Ensure proper indexes on frequently queried fields
2. **Caching** - Implement caching for analytics queries
3. **Background Processing** - Use async processing for heavy analysis tasks
4. **Data Archiving** - Archive old feedback data to maintain performance
