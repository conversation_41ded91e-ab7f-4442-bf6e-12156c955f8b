BDD Testing Framework
=====================

.. image:: https://img.shields.io/badge/framework-pytest--bdd-blue.svg
   :alt: pytest-bdd

.. image:: https://img.shields.io/badge/language-Gherkin-green.svg
   :alt: Gherkin

Overview
--------

The Behavior-Driven Development (BDD) testing framework enables stakeholder-readable test specifications using Gherkin syntax. This approach bridges the gap between technical implementation and business requirements, ensuring that security and functionality requirements are clearly understood and validated.

BDD Architecture
---------------

.. mermaid::

   graph TB
       A[Stakeholder Requirements] --> B[Gherkin Features]
       B --> C[Step Definitions]
       C --> D[Test Implementation]
       D --> E[Automated Execution]
       E --> F[Test Reports]
       
       B --> B1[vulnerability_assessment.feature]
       C --> C1[test_vulnerability_assessment_steps.py]
       D --> D1[Security Service Integration]
       D --> D2[Database Validation]
       D --> D3[API Testing]
       
       F --> F1[HTML Reports]
       F --> F2[JUnit XML]
       F --> F3[Coverage Reports]

Feature Structure
----------------

BDD tests are organized into feature files that describe system behavior from a user perspective:

.. code-block:: gherkin

   Feature: Vulnerability Assessment Security Testing
     As a security engineer
     I want to ensure vulnerability assessments are accurate and secure
     So that critical vulnerabilities are identified within SLA requirements

     Background:
       Given the vulnerability assessment system is running
       And the threat intelligence feeds are active
       And the security scanning tools are configured

     @security @bdd @critical
     Scenario: Critical Asset Vulnerability Assessment
       Given a vulnerability assessment request for critical asset "production-database"
       And the asset has criticality level "CRITICAL"
       And the assessment scope is "FULL"
       When the automated scanner processes the request
       Then all critical vulnerabilities should be identified within SLA
       And the scan duration should be less than 4 hours
       And the false positive rate should be less than 10%

Test Scenarios
-------------

**Security-Focused Scenarios**

.. mermaid::

   graph LR
       A[Security Scenarios] --> B[Vulnerability Assessment]
       A --> C[Threat Intelligence]
       A --> D[Compliance Validation]
       A --> E[Security Controls]
       
       B --> B1[Critical Asset Scanning]
       B --> B2[Multi-Framework Validation]
       B --> B3[Performance Under Load]
       
       C --> C1[IOC Integration]
       C --> C2[Threat Actor Attribution]
       C --> C3[Risk Assessment Updates]
       
       D --> D1[SOC 2 Compliance]
       D --> D2[Audit Trail Validation]
       D --> D3[Control Effectiveness]
       
       E --> E1[Regression Testing]
       E --> E2[Control Validation]
       E --> E3[Remediation Verification]

**Key Test Scenarios:**

1. **Critical Asset Vulnerability Assessment**
   - Validates comprehensive scanning of critical infrastructure
   - Ensures SLA compliance for scan duration and accuracy
   - Verifies CVSS scoring and MITRE ATT&CK mapping

2. **Multi-Framework Security Validation**
   - Tests integration between CVSS 4.0, MITRE ATT&CK, and NIST CSF 2.0
   - Validates framework correlation and consistency
   - Ensures proper control mapping

3. **High-Load Vulnerability Scanning**
   - Tests system performance under concurrent scanning loads
   - Validates resource management and queue processing
   - Ensures data integrity under stress

4. **Security Control Effectiveness Validation**
   - Tests remediation effectiveness
   - Validates security control implementation
   - Ensures regression prevention

5. **Threat Intelligence Integration**
   - Tests real-time threat feed integration
   - Validates IOC correlation and threat attribution
   - Ensures risk assessment updates

6. **Compliance Framework Validation**
   - Tests SOC 2 compliance requirements
   - Validates audit trail maintenance
   - Ensures compliance gap identification

Step Definitions
---------------

Step definitions implement the Gherkin scenarios using pytest-bdd:

.. code-block:: python

   @given("the vulnerability assessment system is running")
   async def step_system_running(context):
       """Ensure the vulnerability assessment system is operational."""
       context.system_start_time = datetime.utcnow()
       context.system_status = "running"
       assert context.system_status == "running"

   @when("the automated scanner processes the request")
   async def step_automated_scanning(context, vulnerability_service):
       """Process the vulnerability assessment request through automated scanning."""
       context.scan_start_time = datetime.utcnow()
       
       context.scan_results = await vulnerability_service.perform_assessment(
           context.assessment_request
       )
       
       context.scan_end_time = datetime.utcnow()
       context.scan_duration = context.scan_end_time - context.scan_start_time

   @then("all critical vulnerabilities should be identified within SLA")
   async def step_validate_critical_vulnerabilities(context):
       """Validate that all critical vulnerabilities are identified within SLA."""
       critical_vulns = [
           vuln for vuln in context.scan_results.vulnerabilities
           if vuln.severity >= VulnerabilitySeverity.HIGH
       ]
       
       assert len(critical_vulns) > 0, "No critical vulnerabilities identified"
       
       for vuln in critical_vulns:
           assert vuln.cvss_score is not None
           assert vuln.cvss_score >= 7.0
           assert vuln.mitre_attack_techniques is not None

Test Execution Flow
------------------

.. mermaid::

   sequenceDiagram
       participant TR as Test Runner
       participant BDD as BDD Framework
       participant SS as Security Service
       participant DB as Database
       participant VS as Vulnerability Scanner
       
       TR->>BDD: Execute Feature
       BDD->>SS: Initialize Security Service
       BDD->>DB: Setup Test Data
       
       Note over BDD: Given Steps
       BDD->>SS: Configure System State
       BDD->>VS: Prepare Scanning Tools
       
       Note over BDD: When Steps
       BDD->>VS: Execute Vulnerability Scan
       VS->>DB: Store Scan Results
       VS->>SS: Validate Security Controls
       
       Note over BDD: Then Steps
       BDD->>DB: Query Results
       BDD->>SS: Validate Security Metrics
       BDD->>TR: Return Test Results

Configuration
------------

BDD tests are configured through pytest markers and fixtures:

.. code-block:: python

   # pytest.ini configuration
   [tool:pytest]
   markers = [
       "bdd: marks tests as behavior-driven development tests",
       "security: marks tests as security tests",
       "critical: marks tests as critical priority",
       "integration: marks tests as integration tests",
       "performance: marks tests as performance tests",
   ]

   # Fixture configuration
   @pytest.fixture
   def bdd_context():
       """BDD testing context."""
       class BDDContext:
           def __init__(self):
               self.system_status = None
               self.threat_feeds = {}
               self.scanning_tools = {}
               self.asset = None
               self.assessment_request = None
               self.scan_results = None
       
       return BDDContext()

Running BDD Tests
----------------

Execute BDD tests using various commands:

.. code-block:: bash

   # Run all BDD tests
   make test-bdd

   # Run specific BDD scenarios
   pytest -v -m bdd tests/bdd/

   # Run with specific tags
   pytest -v -m "bdd and security" tests/bdd/

   # Generate HTML report
   pytest -v -m bdd tests/bdd/ --html=bdd-report.html --self-contained-html

   # Run with coverage
   pytest -v -m bdd tests/bdd/ --cov=src --cov-report=html

Test Data Management
-------------------

BDD tests use structured test data for consistent scenarios:

.. code-block:: python

   # Test data fixtures
   @pytest.fixture
   def sample_vulnerability_data():
       return {
           "title": "Test SQL Injection",
           "description": "SQL injection vulnerability for testing",
           "severity": "HIGH",
           "cvss_score": 8.5,
           "mitre_attack_techniques": ["T1190", "T1078"],
           "asset_id": "test-asset-id",
       }

   @pytest.fixture
   def sample_asset_data():
       return {
           "name": "production-database",
           "asset_type": "database",
           "criticality": "CRITICAL",
           "ip_address": "**********",
           "hostname": "prod-db.internal.com",
       }

Reporting and Analytics
----------------------

BDD tests generate comprehensive reports for stakeholder review:

**HTML Reports**
   - Scenario execution results
   - Step-by-step validation
   - Screenshots for failed tests
   - Execution timeline

**JUnit XML**
   - CI/CD integration
   - Test result aggregation
   - Failure analysis

**Coverage Reports**
   - Code coverage metrics
   - Feature coverage analysis
   - Gap identification

Best Practices
--------------

**Scenario Design**
   - Use business language, not technical jargon
   - Focus on user outcomes, not implementation details
   - Keep scenarios independent and atomic
   - Use meaningful test data

**Step Implementation**
   - Keep steps simple and focused
   - Reuse common steps across scenarios
   - Use proper error handling and assertions
   - Implement proper cleanup

**Maintenance**
   - Regular review of scenario relevance
   - Update scenarios with requirement changes
   - Maintain test data consistency
   - Monitor test execution performance

Integration with CI/CD
---------------------

BDD tests integrate seamlessly with continuous integration:

.. code-block:: yaml

   # GitHub Actions workflow
   - name: Run BDD tests
     run: |
       pytest -v -m bdd tests/bdd/ --html=bdd-report.html --self-contained-html
   
   - name: Upload BDD reports
     uses: actions/upload-artifact@v3
     if: always()
     with:
       name: bdd-reports
       path: bdd-report.html

The BDD framework ensures that security requirements are clearly defined, automatically validated, and continuously monitored throughout the development lifecycle.
