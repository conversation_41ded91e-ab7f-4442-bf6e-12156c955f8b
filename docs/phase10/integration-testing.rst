Integration Testing Framework
=============================

.. image:: https://img.shields.io/badge/framework-TestContainers-blue.svg
   :alt: TestContainers

.. image:: https://img.shields.io/badge/containers-Docker-blue.svg
   :alt: Docker

Overview
--------

The Integration Testing Framework uses TestContainers to provide isolated, reproducible testing environments for multi-service integration validation. This approach ensures that all system components work together correctly while maintaining test isolation and consistency.

Integration Architecture
-----------------------

.. mermaid::

   graph TB
       A[Integration Tests] --> B[TestContainers]
       A --> C[Service Integration]
       A --> D[Database Testing]
       A --> E[API Integration]
       
       B --> B1[PostgreSQL Container]
       B --> B2[Redis Container]
       B --> B3[Neo4j Container]
       B --> B4[InfluxDB Container]
       
       C --> C1[Vulnerability Service]
       C --> C2[Pentester Service]
       C --> C3[Project Service]
       C --> C4[Security Service]
       
       D --> D1[Data Consistency]
       D --> D2[Transaction Testing]
       D --> D3[Migration Validation]
       
       E --> E1[REST API Testing]
       E --> E2[GraphQL Integration]
       E --> E3[WebSocket Testing]

Container Management
-------------------

**Database Containers**

.. mermaid::

   graph LR
       A[Test Suite] --> B[Container Manager]
       B --> C[PostgreSQL]
       B --> D[Redis]
       B --> E[Neo4j]
       B --> F[InfluxDB]
       
       C --> C1[Relational Data]
       C --> C2[Transactions]
       C --> C3[Constraints]
       
       D --> D1[Caching]
       D --> D2[Sessions]
       D --> D3[Pub/Sub]
       
       E --> E1[Graph Data]
       E --> E2[Relationships]
       E --> E3[Analytics]
       
       F --> F1[Time Series]
       F --> F2[Metrics]
       F --> F3[Monitoring]

**Container Configuration**

.. code-block:: python

   @pytest.fixture(scope="session")
   def postgres_container():
       """PostgreSQL container for testing."""
       with PostgresContainer(
           "postgres:15",
           username="test_user",
           password="test_password",
           dbname="test_pitas",
           port=5432
       ) as postgres:
           yield postgres

   @pytest.fixture(scope="session")
   def redis_container():
       """Redis container for caching and session management."""
       with RedisContainer("redis:7-alpine") as redis:
           yield redis

   @pytest.fixture(scope="session")
   def neo4j_container():
       """Neo4j container for graph database operations."""
       with GenericContainer("neo4j:5.14") as neo4j:
           neo4j.with_exposed_ports(7474, 7687)
           neo4j.with_env("NEO4J_AUTH", "neo4j/testpassword")
           neo4j.with_env("NEO4J_PLUGINS", '["apoc"]')
           yield neo4j

Multi-Service Integration
------------------------

**Service Layer Testing**

.. mermaid::

   sequenceDiagram
       participant T as Test
       participant PS as Pentester Service
       participant PRS as Project Service
       participant VS as Vulnerability Service
       participant DB as Database
       participant Cache as Redis
       
       T->>PS: Create Pentester
       PS->>DB: Store Pentester Data
       T->>PRS: Create Project
       PRS->>DB: Store Project Data
       T->>PRS: Assign Pentester to Project
       PRS->>DB: Create Assignment
       T->>VS: Create Vulnerability
       VS->>DB: Store Vulnerability
       VS->>Cache: Cache Results
       T->>T: Validate Data Consistency

**Cross-Service Data Flow**

.. code-block:: python

   @pytest.mark.integration
   @pytest.mark.asyncio
   async def test_service_layer_integration(
       self,
       vulnerability_service,
       pentester_service,
       project_service
   ):
       """Test integration between service layers."""
       
       # Create a pentester
       pentester_data = {
           "email": "<EMAIL>",
           "full_name": "Test Pentester",
           "role": "senior_pentester",
           "skills": ["web_app", "network", "mobile"],
           "certifications": ["OSCP", "CEH"],
       }
       
       pentester = await pentester_service.create(pentester_data)
       assert pentester.id is not None
       
       # Create a project
       project_data = {
           "name": "Integration Test Project",
           "description": "Project for integration testing",
           "client_name": "Test Client",
           "project_type": "web_application",
           "start_date": "2024-01-01",
           "end_date": "2024-12-31",
       }
       
       project = await project_service.create(project_data)
       assert project.id is not None
       
       # Assign pentester to project
       assignment = await project_service.assign_pentester(
           project.id, pentester.id, "lead_pentester"
       )
       assert assignment is not None
       
       # Create vulnerability for the project
       vulnerability_data = {
           "title": "SQL Injection in Login Form",
           "description": "SQL injection vulnerability found in login form",
           "severity": "HIGH",
           "cvss_score": 8.5,
           "project_id": project.id,
           "discovered_by": pentester.id,
       }
       
       vulnerability = await vulnerability_service.create(vulnerability_data)
       assert vulnerability.id is not None
       assert vulnerability.project_id == project.id

Database Integration Testing
---------------------------

**Data Consistency Validation**

.. mermaid::

   graph TB
       A[Data Consistency Tests] --> B[Referential Integrity]
       A --> C[Transaction Isolation]
       A --> D[Concurrent Access]
       A --> E[Migration Testing]
       
       B --> B1[Foreign Key Constraints]
       B --> B2[Cascade Operations]
       B --> B3[Orphan Prevention]
       
       C --> C1[ACID Properties]
       C --> C2[Isolation Levels]
       C --> C3[Deadlock Prevention]
       
       D --> D1[Race Conditions]
       D --> D2[Lock Management]
       D --> D3[Connection Pooling]
       
       E --> E1[Schema Changes]
       E --> E2[Data Migration]
       E --> E3[Rollback Testing]

**Cross-Database Synchronization**

.. code-block:: python

   @pytest.mark.integration
   @pytest.mark.asyncio
   async def test_data_consistency_across_services(
       self,
       vulnerability_service,
       pentester_service,
       project_service,
       test_environment
   ):
       """Test data consistency across multiple services and databases."""
       
       # Create test data
       pentester = await pentester_service.create({
           "email": "<EMAIL>",
           "full_name": "Consistency Test User",
           "role": "pentester",
       })
       
       project = await project_service.create({
           "name": "Consistency Test Project",
           "description": "Testing data consistency",
           "client_name": "Test Client",
       })
       
       vulnerability = await vulnerability_service.create({
           "title": "Consistency Test Vulnerability",
           "description": "Testing data consistency",
           "severity": "HIGH",
           "project_id": project.id,
           "discovered_by": pentester.id,
       })
       
       # Verify data consistency across services
       project_vulns = await vulnerability_service.get_by_project(project.id)
       assert any(v.id == vulnerability.id for v in project_vulns)
       
       pentester_vulns = await vulnerability_service.get_by_discoverer(pentester.id)
       assert any(v.id == vulnerability.id for v in pentester_vulns)

Performance Under Load
---------------------

**Concurrent Operation Testing**

.. mermaid::

   graph LR
       A[Load Testing] --> B[Concurrent Writes]
       A --> C[Read Scalability]
       A --> D[Cache Performance]
       A --> E[Connection Management]
       
       B --> B1[Bulk Inserts]
       B --> B2[Parallel Updates]
       B --> B3[Transaction Throughput]
       
       C --> C1[Query Performance]
       C --> C2[Index Utilization]
       C --> C3[Result Caching]
       
       D --> D1[Cache Hit Rates]
       D --> D2[Invalidation Strategy]
       D --> D3[Memory Usage]
       
       E --> E1[Pool Sizing]
       E --> E2[Connection Reuse]
       E --> E3[Timeout Handling]

**High-Load Integration Testing**

.. code-block:: python

   @pytest.mark.integration
   @pytest.mark.performance
   async def test_high_load_vulnerability_processing(
       self,
       vulnerability_service,
       test_environment
   ):
       """Test system performance under high vulnerability processing load."""
       import time
       
       # Create multiple vulnerabilities concurrently
       vulnerability_count = 100
       start_time = time.time()
       
       tasks = []
       for i in range(vulnerability_count):
           vuln_data = {
               "title": f"Test Vulnerability {i}",
               "description": f"Test vulnerability number {i}",
               "severity": "MEDIUM",
               "cvss_score": 5.0 + (i % 5),
               "cve_id": f"CVE-2024-{i:04d}",
           }
           task = vulnerability_service.create(vuln_data)
           tasks.append(task)
       
       # Execute all tasks concurrently
       results = await asyncio.gather(*tasks, return_exceptions=True)
       
       end_time = time.time()
       processing_time = end_time - start_time
       
       # Verify all vulnerabilities were created successfully
       successful_creates = [r for r in results if not isinstance(r, Exception)]
       assert len(successful_creates) == vulnerability_count
       
       # Performance assertion
       assert processing_time < 30.0, f"Processing took {processing_time:.2f}s, expected < 30s"

Graph Database Integration
-------------------------

**Neo4j Relationship Testing**

.. mermaid::

   graph TB
       A[Graph Integration] --> B[Node Creation]
       A --> C[Relationship Mapping]
       A --> D[Query Performance]
       A --> E[Data Synchronization]
       
       B --> B1[Vulnerability Nodes]
       B --> B2[Asset Nodes]
       B --> B3[Threat Nodes]
       
       C --> C1[Affects Relationships]
       C --> C2[Exploits Relationships]
       C --> C3[Mitigates Relationships]
       
       D --> D1[Cypher Queries]
       D --> D2[Index Usage]
       D --> D3[Traversal Performance]
       
       E --> E1[PostgreSQL Sync]
       E --> E2[Real-time Updates]
       E --> E3[Consistency Checks]

**Vulnerability Correlation Testing**

.. code-block:: python

   @pytest.mark.integration
   @pytest.mark.asyncio
   async def test_vulnerability_correlation_workflow(
       self,
       vulnerability_service,
       test_environment
   ):
       """Test vulnerability correlation using Neo4j graph database."""
       
       # Create multiple related vulnerabilities
       vulnerabilities = [
           {
               "title": "SQL Injection in User Login",
               "description": "SQL injection in login endpoint",
               "severity": "HIGH",
               "cvss_score": 8.5,
               "cve_id": "CVE-2024-0001",
               "mitre_attack_techniques": ["T1190", "T1078"],
           },
           {
               "title": "Privilege Escalation via SQL Injection",
               "description": "Privilege escalation through SQL injection",
               "severity": "CRITICAL",
               "cvss_score": 9.2,
               "cve_id": "CVE-2024-0002",
               "mitre_attack_techniques": ["T1068", "T1078"],
           },
       ]
       
       created_vulns = []
       for vuln_data in vulnerabilities:
           vuln = await vulnerability_service.create(vuln_data)
           created_vulns.append(vuln)
       
       # Test vulnerability correlation
       correlations = await vulnerability_service.find_correlations(
           created_vulns[0].id
       )
       
       # Should find correlations based on MITRE techniques
       assert len(correlations) > 0
       
       # Verify correlation quality
       for correlation in correlations:
           assert correlation.confidence_score > 0.5
           assert len(correlation.common_techniques) > 0

Time-Series Data Integration
---------------------------

**InfluxDB Metrics Testing**

.. mermaid::

   sequenceDiagram
       participant T as Test
       participant VS as Vulnerability Service
       participant IDB as InfluxDB
       participant PG as PostgreSQL
       
       T->>VS: Create Vulnerability
       VS->>PG: Store Vulnerability Data
       VS->>IDB: Record Metrics
       T->>VS: Query Metrics
       VS->>IDB: Fetch Time Series Data
       IDB->>VS: Return Metrics
       VS->>T: Return Aggregated Data
       T->>T: Validate Metrics

**Metrics Validation**

.. code-block:: python

   @pytest.mark.integration
   async def test_metrics_integration(self, vulnerability_service, influxdb_container):
       """Test integration with InfluxDB for metrics collection."""
       
       # Create vulnerabilities to generate metrics
       for i in range(10):
           await vulnerability_service.create({
               "title": f"Metrics Test Vulnerability {i}",
               "severity": "HIGH" if i % 2 == 0 else "MEDIUM",
               "cvss_score": 7.0 + i * 0.1,
           })
       
       # Query metrics
       metrics = await vulnerability_service.get_metrics(
           time_range="1h",
           aggregation="count"
       )
       
       # Validate metrics
       assert metrics["total_vulnerabilities"] >= 10
       assert "severity_distribution" in metrics
       assert "cvss_distribution" in metrics

Test Environment Management
--------------------------

**Container Lifecycle**

.. mermaid::

   graph TB
       A[Test Session Start] --> B[Container Startup]
       B --> C[Database Initialization]
       C --> D[Service Configuration]
       D --> E[Test Execution]
       E --> F[Data Cleanup]
       F --> G[Container Shutdown]
       
       B --> B1[Pull Images]
       B --> B2[Start Containers]
       B --> B3[Wait for Ready]
       
       C --> C1[Create Schemas]
       C --> C2[Run Migrations]
       C --> C3[Seed Test Data]
       
       F --> F1[Clear Test Data]
       F --> F2[Reset Sequences]
       F --> F3[Cleanup Resources]

**Environment Configuration**

.. code-block:: python

   @pytest.fixture(scope="session")
   def test_environment(postgres_container, redis_container, neo4j_container, influxdb_container):
       """Complete test environment with all services."""
       return {
           "postgres": postgres_container,
           "redis": redis_container,
           "neo4j": neo4j_container,
           "influxdb": influxdb_container,
       }

   @pytest.fixture
   async def test_settings(test_environment):
       """Test settings with container connection strings."""
       postgres = test_environment["postgres"]
       redis = test_environment["redis"]
       neo4j = test_environment["neo4j"]
       influxdb = test_environment["influxdb"]
       
       settings = Settings(
           database_url=postgres.get_connection_url(),
           redis_url=redis.get_connection_url(),
           neo4j_uri=f"bolt://localhost:{neo4j.get_exposed_port(7687)}",
           influxdb_url=f"http://localhost:{influxdb.get_exposed_port(8086)}",
       )
       
       return settings

Running Integration Tests
------------------------

Execute integration tests with various configurations:

.. code-block:: bash

   # Run all integration tests
   make test-containers

   # Run with specific database
   pytest -v -m integration tests/integration/

   # Run with parallel execution
   pytest -v -m integration tests/integration/ -n auto

   # Generate coverage report
   pytest -v -m integration tests/integration/ --cov=src --cov-report=html

   # Run with detailed logging
   pytest -v -m integration tests/integration/ --log-level=DEBUG

The integration testing framework ensures that all system components work together correctly while maintaining data consistency, performance, and reliability across different database technologies and service interactions.
