End-to-End Testing Framework
============================

.. image:: https://img.shields.io/badge/framework-Playwright-blue.svg
   :alt: Playwright

.. image:: https://img.shields.io/badge/browsers-Chrome%20|%20Firefox%20|%20Safari-green.svg
   :alt: Cross-browser

Overview
--------

The End-to-End (E2E) testing framework uses <PERSON><PERSON> to validate complete user journeys and security controls across multiple browsers. This framework focuses on authentication security, user interface validation, and cross-browser compatibility testing.

E2E Architecture
---------------

.. mermaid::

   graph TB
       A[E2E Test Suite] --> B[Browser Automation]
       A --> C[Security Validation]
       A --> D[User Journey Testing]
       A --> E[Cross-browser Testing]
       
       B --> B1[Playwright Engine]
       B --> B2[Page Objects]
       B --> B3[Test Fixtures]
       
       C --> C1[Authentication Security]
       C --> C2[XSS Protection]
       C --> C3[CSRF Validation]
       C --> C4[Session Management]
       
       D --> D1[Login Flow]
       D --> D2[Dashboard Navigation]
       D --> D3[Project Management]
       D --> D4[Vulnerability Reporting]
       
       E --> E1[Chromium]
       E --> E2[Firefox]
       E --> E3[WebKit/Safari]

Security Testing Focus
---------------------

The E2E framework prioritizes security validation across all user interactions:

.. mermaid::

   graph LR
       A[Security Tests] --> B[Authentication]
       A --> C[Authorization]
       A --> D[Input Validation]
       A --> E[Session Security]
       
       B --> B1[Login Security]
       B --> B2[Password Policy]
       B --> B3[Account Lockout]
       B --> B4[Multi-factor Auth]
       
       C --> C1[Role-based Access]
       C --> C2[Resource Protection]
       C --> C3[API Authorization]
       
       D --> D1[XSS Prevention]
       D --> D2[SQL Injection Protection]
       D --> D3[Command Injection Prevention]
       D --> D4[Path Traversal Protection]
       
       E --> E1[Session Cookies]
       E --> E2[CSRF Tokens]
       E --> E3[Session Timeout]
       E --> E4[Secure Logout]

Authentication Security Tests
----------------------------

**Login Form Security Validation**

.. code-block:: python

   @pytest.mark.e2e
   @pytest.mark.security
   async def test_login_form_security_headers(self, page: Page):
       """Test that login page includes required security headers."""
       response = await page.goto(f"{config.base_url}/login")
       
       # Verify response status
       assert response.status == 200
       
       # Check security headers
       headers = response.headers
       
       # Content Security Policy
       assert "content-security-policy" in headers
       csp = headers["content-security-policy"]
       assert "default-src 'self'" in csp
       
       # X-Frame-Options (clickjacking protection)
       assert "x-frame-options" in headers
       assert headers["x-frame-options"].upper() in ["DENY", "SAMEORIGIN"]
       
       # X-Content-Type-Options
       assert "x-content-type-options" in headers
       assert headers["x-content-type-options"] == "nosniff"

**XSS Protection Testing**

.. mermaid::

   sequenceDiagram
       participant T as Test
       participant B as Browser
       participant A as Application
       participant S as Security Layer
       
       T->>B: Navigate to form
       T->>B: Input XSS payload
       B->>A: Submit form data
       A->>S: Validate input
       S->>A: Block/sanitize payload
       A->>B: Return safe response
       B->>T: Verify no script execution
       T->>T: Assert XSS prevented

**SQL Injection Protection**

.. code-block:: python

   @pytest.mark.e2e
   @pytest.mark.security
   async def test_sql_injection_protection(self, page: Page):
       """Test SQL injection protection in login form."""
       await page.goto(f"{config.base_url}/login")
       
       # Test SQL injection payloads
       for payload in config.security_tests["sql_injection_payloads"]:
           await page.fill('input[name="username"]', payload)
           await page.fill('input[name="password"]', "testpassword")
           await page.click('button[type="submit"]')
           
           # Wait for response
           await page.wait_for_timeout(1000)
           
           # Verify we're not logged in (SQL injection failed)
           current_url = page.url
           assert "/dashboard" not in current_url
           assert "/login" in current_url or "/auth" in current_url

Session Management Testing
-------------------------

**Session Security Validation**

.. mermaid::

   graph TB
       A[Session Tests] --> B[Cookie Security]
       A --> C[Session Timeout]
       A --> D[Secure Logout]
       A --> E[Session Fixation]
       
       B --> B1[HttpOnly Flag]
       B --> B2[Secure Flag]
       B --> B3[SameSite Attribute]
       B --> B4[Expiration Time]
       
       C --> C1[Idle Timeout]
       C --> C2[Absolute Timeout]
       C --> C3[Activity Tracking]
       
       D --> D1[Session Invalidation]
       D --> D2[Cookie Cleanup]
       D --> D3[Redirect to Login]
       
       E --> E1[Session Regeneration]
       E --> E2[Token Validation]
       E --> E3[CSRF Protection]

**CSRF Protection Testing**

.. code-block:: python

   @pytest.mark.e2e
   @pytest.mark.security
   async def test_csrf_protection(self, page: Page):
       """Test CSRF protection on forms."""
       # Login first
       await self.login(page)
       
       # Navigate to a form page
       await page.goto(f"{config.base_url}/projects/create")
       
       # Check for CSRF token in form
       csrf_token_input = await page.query_selector('input[name="csrf_token"]')
       if csrf_token_input:
           csrf_value = await csrf_token_input.get_attribute("value")
           assert csrf_value is not None and len(csrf_value) > 10
       
       # Test form submission without CSRF token
       if csrf_token_input:
           await page.evaluate("document.querySelector('input[name=\"csrf_token\"]').remove()")
       
       # Submit form and expect failure
       await page.fill('input[name="name"]', "Test Project")
       await page.click('button[type="submit"]')
       
       # Should show error or redirect back
       await page.wait_for_timeout(2000)
       error_elements = await page.query_selector_all(".error, .alert-danger")
       assert len(error_elements) > 0

Cross-Browser Testing
--------------------

**Browser Configuration**

.. code-block:: python

   # Playwright configuration for multiple browsers
   BROWSERS = ["chromium", "firefox", "webkit"]
   
   @pytest.fixture(params=BROWSERS)
   async def browser_type(request):
       """Parameterized fixture for cross-browser testing."""
       return request.param
   
   @pytest.fixture
   async def browser(browser_type):
       """Browser instance for testing."""
       async with async_playwright() as p:
           if browser_type == "chromium":
               browser = await p.chromium.launch(**config.get_browser_config("chromium"))
           elif browser_type == "firefox":
               browser = await p.firefox.launch(**config.get_browser_config("firefox"))
           elif browser_type == "webkit":
               browser = await p.webkit.launch(**config.get_browser_config("webkit"))
           
           yield browser
           await browser.close()

**Browser-Specific Security Tests**

.. mermaid::

   graph LR
       A[Cross-browser Tests] --> B[Chromium Tests]
       A --> C[Firefox Tests]
       A --> D[WebKit Tests]
       
       B --> B1[Chrome Security Features]
       B --> B2[V8 Engine Validation]
       B --> B3[Chrome Extensions]
       
       C --> C1[Firefox Security]
       C --> C2[Gecko Engine]
       C --> C3[Firefox Add-ons]
       
       D --> D1[Safari Security]
       D --> D2[WebKit Engine]
       D --> D3[iOS Compatibility]

User Journey Testing
-------------------

**Complete Authentication Flow**

.. mermaid::

   sequenceDiagram
       participant U as User
       participant B as Browser
       participant A as Auth Service
       participant D as Dashboard
       participant API as API Gateway
       
       U->>B: Navigate to login
       B->>A: Request login page
       A->>B: Return login form
       U->>B: Enter credentials
       B->>A: Submit login
       A->>A: Validate credentials
       A->>B: Set session cookie
       B->>D: Redirect to dashboard
       D->>API: Request user data
       API->>D: Return dashboard data
       D->>B: Render dashboard
       B->>U: Display dashboard

**Project Management Workflow**

.. code-block:: python

   @pytest.mark.e2e
   async def test_complete_project_workflow(self, page: Page):
       """Test complete project management workflow."""
       # Login
       await self.login(page)
       
       # Navigate to projects
       await page.click('a[href="/projects"]')
       await page.wait_for_url("**/projects")
       
       # Create new project
       await page.click('button[data-action="create-project"]')
       await page.fill('input[name="name"]', "E2E Test Project")
       await page.fill('textarea[name="description"]', "Project created via E2E test")
       await page.click('button[type="submit"]')
       
       # Verify project creation
       await page.wait_for_selector('.project-card:has-text("E2E Test Project")')
       
       # Edit project
       await page.click('.project-card:has-text("E2E Test Project") .edit-button')
       await page.fill('input[name="name"]', "Updated E2E Test Project")
       await page.click('button[type="submit"]')
       
       # Verify update
       await page.wait_for_selector('.project-card:has-text("Updated E2E Test Project")')

Performance Testing
------------------

**Page Load Performance**

.. code-block:: python

   @pytest.mark.e2e
   @pytest.mark.performance
   async def test_page_load_performance(self, page: Page):
       """Test page load performance meets requirements."""
       # Measure page load time
       start_time = time.time()
       await page.goto(f"{config.base_url}/dashboard")
       await page.wait_for_load_state("networkidle")
       end_time = time.time()
       
       load_time = end_time - start_time
       
       # Assert performance requirements
       assert load_time < config.performance_thresholds["page_load"], \
           f"Page load time {load_time:.2f}s exceeds threshold"

**API Response Performance**

.. mermaid::

   graph TB
       A[Performance Tests] --> B[Page Load Time]
       A --> C[API Response Time]
       A --> D[Resource Loading]
       A --> E[Interactive Time]
       
       B --> B1[First Contentful Paint]
       B --> B2[Largest Contentful Paint]
       B --> B3[Time to Interactive]
       
       C --> C1[Authentication API]
       C --> C2[Data Fetching API]
       C --> C3[Form Submission API]
       
       D --> D1[CSS Loading]
       D --> D2[JavaScript Loading]
       D --> D3[Image Loading]
       
       E --> E1[Button Responsiveness]
       E --> E2[Form Interaction]
       E --> E3[Navigation Speed]

Test Configuration
-----------------

**Playwright Configuration**

.. code-block:: python

   # playwright.config.py
   class PlaywrightConfig:
       def __init__(self):
           self.base_url = "http://localhost:8000"
           self.headless = True
           self.timeout = 30000
           self.browsers = ["chromium", "firefox", "webkit"]
           self.viewport = {"width": 1280, "height": 720}
       
       def get_browser_config(self, browser_name: str) -> dict:
           base_config = {
               "headless": self.headless,
               "viewport": self.viewport,
               "ignore_https_errors": True,
           }
           
           if browser_name == "chromium":
               base_config.update({
                   "args": [
                       "--disable-web-security",
                       "--no-sandbox",
                       "--disable-dev-shm-usage",
                   ]
               })
           
           return base_config

**Test Data Management**

.. code-block:: python

   # Test users for different scenarios
   TEST_USERS = {
       "admin": {
           "username": "<EMAIL>",
           "password": "SecureTestPassword123!",
           "role": "admin",
       },
       "pentester": {
           "username": "<EMAIL>",
           "password": "SecureTestPassword123!",
           "role": "pentester",
       },
   }

Running E2E Tests
----------------

Execute E2E tests with various configurations:

.. code-block:: bash

   # Install Playwright browsers
   playwright install --with-deps

   # Run all E2E tests
   make test-e2e

   # Run with specific browser
   pytest -v -m e2e tests/e2e/ --browser chromium

   # Run in headed mode (visible browser)
   pytest -v -m e2e tests/e2e/ --headed

   # Generate test report
   pytest -v -m e2e tests/e2e/ --html=e2e-report.html --self-contained-html

   # Run with video recording
   pytest -v -m e2e tests/e2e/ --video=on

Test Reporting
-------------

E2E tests generate comprehensive reports including:

**HTML Reports**
   - Test execution results
   - Screenshots of failures
   - Video recordings of test runs
   - Performance metrics

**Trace Files**
   - Detailed execution traces
   - Network activity logs
   - Console output capture
   - DOM snapshots

**Performance Metrics**
   - Page load times
   - API response times
   - Resource loading metrics
   - User interaction timing

The E2E testing framework ensures that all user-facing functionality works correctly across different browsers while maintaining security standards and performance requirements.
