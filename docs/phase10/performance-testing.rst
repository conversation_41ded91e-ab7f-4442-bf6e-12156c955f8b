Performance Testing Framework
=============================

.. image:: https://img.shields.io/badge/framework-Locust-green.svg
   :alt: Locust

.. image:: https://img.shields.io/badge/monitoring-Real--time-blue.svg
   :alt: Real-time Monitoring

Overview
--------

The Performance Testing Framework provides comprehensive load testing, stress testing, and performance monitoring capabilities using Locust and custom performance validation tools. This framework ensures that the PITAS system maintains optimal performance under various load conditions while meeting strict SLA requirements.

Performance Architecture
-----------------------

.. mermaid::

   graph TB
       A[Performance Testing] --> B[Load Testing]
       A --> C[Stress Testing]
       A --> D[Performance Monitoring]
       A --> E[Capacity Planning]
       
       B --> B1[Concurrent Users]
       B --> B2[Request Throughput]
       B --> B3[Response Times]
       
       C --> C1[Breaking Point]
       C --> C2[Resource Limits]
       C --> C3[Recovery Testing]
       
       D --> D1[Real-time Metrics]
       D --> D2[Performance Alerts]
       D --> D3[Trend Analysis]
       
       E --> E1[Scalability Testing]
       E --> E2[Resource Planning]
       E --> E3[Growth Projections]

Load Testing with Locust
------------------------

**Locust User Simulation**

.. mermaid::

   sequenceDiagram
       participant LM as Locust Master
       participant LW as Locust Workers
       participant A as Application
       participant DB as Database
       participant C as Cache
       
       LM->>LW: Distribute load test
       loop Concurrent Users
           LW->>A: Send HTTP requests
           A->>DB: Query database
           A->>C: Check cache
           C->>A: Return cached data
           DB->>A: Return query results
           A->>LW: Return response
           LW->>LM: Report metrics
       end
       LM->>LM: Aggregate results

**User Behavior Simulation**

.. code-block:: python

   class PitasLoadTestUser(HttpUser):
       """Locust user class for load testing PITAS."""
       
       wait_time = between(1, 3)  # Wait 1-3 seconds between tasks
       
       def on_start(self):
           """Called when a user starts."""
           self.login()
       
       def login(self):
           """Authenticate user for testing."""
           response = self.client.post("/api/v1/auth/login", json={
               "username": "<EMAIL>",
               "password": "LoadTestPassword123!"
           })
           
           if response.status_code == 200:
               token = response.json().get("access_token")
               self.client.headers.update({"Authorization": f"Bearer {token}"})
       
       @task(3)
       def view_dashboard(self):
           """Load dashboard - most common operation."""
           self.client.get("/api/v1/dashboard")
       
       @task(2)
       def list_projects(self):
           """List projects."""
           self.client.get("/api/v1/projects/")
       
       @task(2)
       def list_vulnerabilities(self):
           """List vulnerabilities."""
           self.client.get("/api/v1/vulnerabilities/")
       
       @task(1)
       def create_vulnerability(self):
           """Create a new vulnerability."""
           vulnerability_data = {
               "title": f"Load Test Vulnerability {time.time()}",
               "description": "Vulnerability created during load testing",
               "severity": "MEDIUM",
               "cvss_score": 5.5,
           }
           self.client.post("/api/v1/vulnerabilities/", json=vulnerability_data)

Performance Metrics Collection
-----------------------------

**Key Performance Indicators**

.. mermaid::

   graph LR
       A[Performance KPIs] --> B[Response Time]
       A --> C[Throughput]
       A --> D[Error Rate]
       A --> E[Resource Utilization]
       
       B --> B1[Average Response Time]
       B --> B2[95th Percentile]
       B --> B3[99th Percentile]
       B --> B4[Maximum Response Time]
       
       C --> C1[Requests per Second]
       C --> C2[Transactions per Minute]
       C --> C3[Data Throughput]
       
       D --> D1[HTTP Error Rate]
       D --> D2[Timeout Rate]
       D --> D3[Connection Errors]
       
       E --> E1[CPU Usage]
       E --> E2[Memory Usage]
       E --> E3[Database Connections]
       E --> E4[Cache Hit Rate]

**Performance Thresholds**

.. code-block:: python

   PERFORMANCE_THRESHOLDS = {
       "api_response_time": 2.0,      # seconds
       "database_query_time": 1.0,    # seconds
       "page_load_time": 3.0,         # seconds
       "concurrent_users": 100,
       "requests_per_second": 50,
       "memory_usage_mb": 512,
       "cpu_usage_percent": 80,
       "error_rate_percent": 1.0,
   }

API Performance Testing
----------------------

**Response Time Validation**

.. code-block:: python

   @pytest.mark.performance
   @pytest.mark.asyncio
   async def test_api_response_times(self, test_client):
       """Test API response times for critical endpoints."""
       
       # Define critical endpoints and their performance thresholds
       endpoints = {
           "/api/v1/health": 0.1,           # Health check should be very fast
           "/api/v1/dashboard": 2.0,        # Dashboard should load within 2 seconds
           "/api/v1/projects/": 1.5,        # Project list within 1.5 seconds
           "/api/v1/vulnerabilities/": 2.0, # Vulnerability list within 2 seconds
           "/api/v1/vulnerabilities/metrics": 3.0,  # Metrics can take up to 3 seconds
       }
       
       for endpoint, threshold in endpoints.items():
           response_times = []
           
           for _ in range(5):  # Test each endpoint 5 times
               start_time = time.time()
               response = await test_client.get(endpoint)
               end_time = time.time()
               
               response_time = end_time - start_time
               response_times.append(response_time)
               
               assert response.status_code in [200, 401]
           
           avg_response_time = statistics.mean(response_times)
           max_response_time = max(response_times)
           
           assert avg_response_time < threshold, \
               f"{endpoint} average response time {avg_response_time:.3f}s exceeds threshold {threshold}s"

**Concurrent Request Testing**

.. mermaid::

   graph TB
       A[Concurrent Testing] --> B[Load Generation]
       A --> C[Response Monitoring]
       A --> D[Resource Tracking]
       A --> E[Error Analysis]
       
       B --> B1[Multiple Clients]
       B --> B2[Request Distribution]
       B --> B3[Load Patterns]
       
       C --> C1[Response Times]
       C --> C2[Success Rates]
       C --> C3[Throughput Metrics]
       
       D --> D1[CPU Monitoring]
       D --> D2[Memory Tracking]
       D --> D3[Database Connections]
       
       E --> E1[Error Classification]
       E --> E2[Failure Analysis]
       E --> E3[Recovery Testing]

Database Performance Testing
---------------------------

**Query Performance Validation**

.. code-block:: python

   @pytest.mark.performance
   @pytest.mark.asyncio
   async def test_database_query_performance(self, test_client):
       """Test database query performance."""
       
       complex_endpoints = {
           "/api/v1/vulnerabilities/search?q=sql": 3.0,
           "/api/v1/projects/statistics/overview": 2.0,
           "/api/v1/pentesters/performance/metrics": 2.5,
           "/api/v1/vulnerabilities/metrics": 3.0,
       }
       
       for endpoint, threshold in complex_endpoints.items():
           response_times = []
           
           for _ in range(3):  # Test each complex query 3 times
               start_time = time.time()
               response = await test_client.get(endpoint)
               end_time = time.time()
               
               response_time = end_time - start_time
               response_times.append(response_time)
               
               assert response.status_code in [200, 401, 404]
           
           avg_response_time = statistics.mean(response_times)
           assert avg_response_time < threshold, \
               f"Database query {endpoint} average time {avg_response_time:.3f}s exceeds threshold {threshold}s"

**Connection Pool Performance**

.. mermaid::

   sequenceDiagram
       participant A as Application
       participant P as Connection Pool
       participant DB as Database
       participant M as Monitor
       
       A->>P: Request connection
       P->>P: Check available connections
       alt Connection available
           P->>A: Return connection
           A->>DB: Execute query
           DB->>A: Return results
           A->>P: Return connection
       else Pool exhausted
           P->>DB: Create new connection
           DB->>P: Connection established
           P->>A: Return connection
       end
       P->>M: Report pool metrics
       M->>M: Track performance

Memory and Resource Testing
--------------------------

**Memory Usage Monitoring**

.. code-block:: python

   @pytest.mark.performance
   @pytest.mark.asyncio
   async def test_memory_usage_under_load(self, test_client):
       """Test memory usage during high load operations."""
       import psutil
       import os
       
       # Get current process
       process = psutil.Process(os.getpid())
       initial_memory = process.memory_info().rss / 1024 / 1024  # MB
       
       # Perform memory-intensive operations
       tasks = []
       for i in range(100):
           task = test_client.get("/api/v1/vulnerabilities/")
           tasks.append(task)
       
       # Execute all requests
       await asyncio.gather(*tasks, return_exceptions=True)
       
       # Check memory usage after operations
       final_memory = process.memory_info().rss / 1024 / 1024  # MB
       memory_increase = final_memory - initial_memory
       
       # Memory should not increase dramatically
       assert memory_increase < 100, \
           f"Memory increased by {memory_increase:.2f}MB, exceeding 100MB threshold"

**Resource Utilization Tracking**

.. mermaid::

   graph TB
       A[Resource Monitoring] --> B[CPU Usage]
       A --> C[Memory Usage]
       A --> D[Disk I/O]
       A --> E[Network I/O]
       
       B --> B1[Process CPU]
       B --> B2[System CPU]
       B --> B3[CPU Cores]
       
       C --> C1[Heap Memory]
       C --> C2[Stack Memory]
       C --> C3[Cache Memory]
       
       D --> D1[Read Operations]
       D --> D2[Write Operations]
       D --> D3[Disk Space]
       
       E --> E1[Inbound Traffic]
       E --> E2[Outbound Traffic]
       E --> E3[Connection Count]

Stress Testing
-------------

**Breaking Point Analysis**

.. code-block:: python

   @pytest.mark.performance
   @pytest.mark.slow
   def test_locust_stress_testing(self):
       """Run Locust-based stress testing to find breaking point."""
       
       # Setup Locust environment
       env = Environment(user_classes=[PitasLoadTestUser])
       env.create_local_runner()
       
       # Gradually increase load
       user_counts = [10, 25, 50, 100, 200, 500]
       results = {}
       
       for user_count in user_counts:
           # Start load test
           env.runner.start(user_count=user_count, spawn_rate=10)
           
           # Run for 60 seconds
           time.sleep(60)
           
           # Collect metrics
           stats = env.stats.total
           results[user_count] = {
               "avg_response_time": stats.avg_response_time,
               "failure_rate": stats.failure_count / stats.num_requests if stats.num_requests > 0 else 0,
               "rps": stats.total_rps,
           }
           
           # Stop if failure rate exceeds threshold
           if results[user_count]["failure_rate"] > 0.05:  # 5% failure rate
               break
           
           env.runner.stop()
       
       # Analyze breaking point
       breaking_point = max([uc for uc, r in results.items() if r["failure_rate"] <= 0.05])
       assert breaking_point >= 100, f"System breaks at {breaking_point} users, expected >= 100"

**Recovery Testing**

.. mermaid::

   graph LR
       A[Recovery Testing] --> B[Overload System]
       B --> C[Monitor Degradation]
       C --> D[Reduce Load]
       D --> E[Measure Recovery]
       E --> F[Validate Stability]
       
       B --> B1[Spike Load]
       B --> B2[Sustained Load]
       B --> B3[Resource Exhaustion]
       
       E --> E1[Response Time Recovery]
       E --> E2[Error Rate Normalization]
       E --> E3[Resource Cleanup]

Performance Reporting
---------------------

**Real-time Performance Dashboard**

.. mermaid::

   graph TB
       A[Performance Dashboard] --> B[Real-time Metrics]
       A --> C[Historical Trends]
       A --> D[Alert Management]
       A --> E[Capacity Planning]
       
       B --> B1[Current RPS]
       B --> B2[Active Users]
       B --> B3[Response Times]
       B --> B4[Error Rates]
       
       C --> C1[Performance Trends]
       C --> C2[Capacity Utilization]
       C --> C3[Growth Patterns]
       
       D --> D1[Threshold Alerts]
       D --> D2[Anomaly Detection]
       D --> D3[Escalation Rules]
       
       E --> E1[Resource Forecasting]
       E --> E2[Scaling Recommendations]
       E --> E3[Cost Optimization]

**Performance Test Reports**

.. code-block:: python

   class PerformanceReporter:
       """Generate comprehensive performance test reports."""
       
       def generate_load_test_report(self, test_results):
           """Generate load test performance report."""
           return {
               "executive_summary": {
                   "max_concurrent_users": test_results.max_users,
                   "avg_response_time": test_results.avg_response_time,
                   "peak_rps": test_results.peak_rps,
                   "error_rate": test_results.error_rate,
                   "sla_compliance": test_results.sla_compliance,
               },
               "performance_metrics": {
                   "response_times": test_results.response_time_distribution,
                   "throughput": test_results.throughput_metrics,
                   "resource_utilization": test_results.resource_metrics,
               },
               "recommendations": self._generate_performance_recommendations(test_results),
               "capacity_planning": self._analyze_capacity_requirements(test_results),
           }

Continuous Performance Monitoring
---------------------------------

**CI/CD Performance Gates**

.. code-block:: yaml

   # Performance testing in CI/CD pipeline
   performance-tests:
     runs-on: ubuntu-latest
     steps:
     - name: Run performance tests
       run: |
         pytest -v -m performance tests/performance/ --html=performance-report.html
     
     - name: Check performance thresholds
       run: |
         python scripts/check_performance_thresholds.py performance-report.html
     
     - name: Upload performance reports
       uses: actions/upload-artifact@v3
       with:
         name: performance-reports
         path: performance-report.html

**Performance Regression Detection**

.. mermaid::

   sequenceDiagram
       participant D as Developer
       participant CI as CI Pipeline
       participant PT as Performance Tests
       participant B as Baseline
       participant A as Alerts
       
       D->>CI: Push code changes
       CI->>PT: Run performance tests
       PT->>PT: Execute load tests
       PT->>B: Compare with baseline
       
       alt Performance regression detected
           B->>A: Trigger performance alert
           A->>D: Notify of regression
           CI->>CI: Block deployment
       else Performance acceptable
           B->>B: Update baseline
           CI->>CI: Continue deployment
       end

Running Performance Tests
------------------------

Execute performance tests with comprehensive monitoring:

.. code-block:: bash

   # Run all performance tests
   make test-performance

   # Run load tests specifically
   make test-load

   # Run with custom user count
   locust -f tests/performance/locustfile.py --users 100 --spawn-rate 10

   # Generate performance report
   pytest -v -m performance tests/performance/ --html=performance-report.html

   # Run stress testing
   pytest -v -m "performance and slow" tests/performance/

   # Monitor real-time performance
   python scripts/performance_monitor.py --duration 3600

The performance testing framework ensures that the PITAS system maintains optimal performance under various load conditions while providing comprehensive monitoring and reporting capabilities for continuous performance optimization.
