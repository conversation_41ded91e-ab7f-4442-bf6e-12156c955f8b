# PITAS Production Deployment Guide

## 🚀 **PITAS System - Ready for Enterprise Deployment**

This guide provides comprehensive instructions for deploying the complete PITAS pentesting team management system in production environments.

## 📋 **Prerequisites**

### Infrastructure Requirements
- **Kubernetes Cluster** (v1.24+) or Docker Swarm
- **PostgreSQL** (v14+) with high availability setup
- **Redis Cluster** (v6+) for caching and session management
- **Load Balancer** (HAProxy, NGINX, or cloud provider)
- **SSL/TLS Certificates** for HTTPS termination
- **Monitoring Stack** (Prometheus, Grafana, Jaeger)

### Resource Requirements
- **Minimum**: 4 CPU cores, 8GB RAM, 100GB storage
- **Recommended**: 8 CPU cores, 16GB RAM, 500GB storage
- **Enterprise**: 16+ CPU cores, 32GB+ RAM, 1TB+ storage

### Security Requirements
- **Network Security**: VPC/VNET with proper segmentation
- **Identity Management**: Enterprise SSO integration
- **Secrets Management**: HashiCorp Vault or cloud provider
- **Backup Strategy**: Automated daily backups with retention

## 🐳 **Docker Deployment**

### 1. Production Docker Compose

Create `docker-compose.prod.yml`:

```yaml
version: '3.8'

services:
  pitas-api:
    image: pitas:latest
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=************************************/pitas
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=production
    depends_on:
      - postgres
      - redis
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '2'
          memory: 4G
        reservations:
          cpus: '1'
          memory: 2G

  postgres:
    image: postgres:14
    environment:
      - POSTGRES_DB=pitas
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G

  redis:
    image: redis:6-alpine
    volumes:
      - redis_data:/data
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 2G

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl/certs
    depends_on:
      - pitas-api

volumes:
  postgres_data:
  redis_data:
```

### 2. Build Production Image

```bash
# Build optimized production image
docker build -f Dockerfile.prod -t pitas:latest .

# Tag for registry
docker tag pitas:latest your-registry.com/pitas:v1.0.0

# Push to registry
docker push your-registry.com/pitas:v1.0.0
```

## ☸️ **Kubernetes Deployment**

### 1. Namespace and ConfigMap

```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: pitas

---
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: pitas-config
  namespace: pitas
data:
  DATABASE_URL: "************************************/pitas"
  REDIS_URL: "redis://redis:6379"
  ENVIRONMENT: "production"
```

### 2. Deployment

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: pitas-api
  namespace: pitas
spec:
  replicas: 3
  selector:
    matchLabels:
      app: pitas-api
  template:
    metadata:
      labels:
        app: pitas-api
    spec:
      containers:
      - name: pitas-api
        image: your-registry.com/pitas:v1.0.0
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: pitas-config
        - secretRef:
            name: pitas-secrets
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 2
            memory: 4Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

### 3. Service and Ingress

```yaml
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: pitas-api-service
  namespace: pitas
spec:
  selector:
    app: pitas-api
  ports:
  - port: 80
    targetPort: 8000
  type: ClusterIP

---
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: pitas-ingress
  namespace: pitas
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - pitas.yourdomain.com
    secretName: pitas-tls
  rules:
  - host: pitas.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: pitas-api-service
            port:
              number: 80
```

## 🗄️ **Database Setup**

### 1. Production PostgreSQL Configuration

```sql
-- Create production database
CREATE DATABASE pitas_prod;
CREATE USER pitas_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE pitas_prod TO pitas_user;

-- Performance optimizations
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
```

### 2. Run Database Migrations

```bash
# Set production environment
export DATABASE_URL="postgresql://pitas_user:secure_password@localhost:5432/pitas_prod"

# Run all migrations
alembic upgrade head

# Verify migration status
alembic current
```

### 3. Create Initial Admin User

```bash
# Create admin user
python scripts/create_admin_user.py \
  --email <EMAIL> \
  --password secure_admin_password \
  --full-name "System Administrator"
```

## 🔐 **Security Configuration**

### 1. Environment Variables

```bash
# .env.production
SECRET_KEY=your-super-secure-secret-key-here
DATABASE_URL=********************************/pitas_prod
REDIS_URL=redis://redis-host:6379
ENVIRONMENT=production

# Security settings
CORS_ORIGINS=["https://pitas.yourdomain.com"]
ALLOWED_HOSTS=["pitas.yourdomain.com"]
SECURE_SSL_REDIRECT=true
SESSION_COOKIE_SECURE=true
CSRF_COOKIE_SECURE=true

# Integration settings
SSO_SAML_METADATA_URL=https://your-idp.com/metadata
SSO_OIDC_DISCOVERY_URL=https://your-oidc-provider.com/.well-known/openid_configuration
NESSUS_API_URL=https://nessus.yourdomain.com
QUALYS_API_URL=https://qualysapi.qualys.com
```

### 2. SSL/TLS Configuration

```nginx
# nginx.conf
server {
    listen 443 ssl http2;
    server_name pitas.yourdomain.com;

    ssl_certificate /etc/ssl/certs/pitas.crt;
    ssl_certificate_key /etc/ssl/private/pitas.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    location / {
        proxy_pass http://pitas-api:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 📊 **Monitoring Setup**

### 1. Prometheus Configuration

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'pitas-api'
    static_configs:
      - targets: ['pitas-api:8000']
    metrics_path: /metrics
    scrape_interval: 15s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
```

### 2. Grafana Dashboards

Import the provided Grafana dashboards:
- `dashboards/pitas-overview.json` - System overview
- `dashboards/pitas-performance.json` - Performance metrics
- `dashboards/pitas-security.json` - Security monitoring

## 🔄 **Backup and Recovery**

### 1. Database Backup Script

```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backups/pitas"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="pitas_prod"

# Create backup
pg_dump $DB_NAME | gzip > "$BACKUP_DIR/pitas_backup_$DATE.sql.gz"

# Cleanup old backups (keep 30 days)
find $BACKUP_DIR -name "pitas_backup_*.sql.gz" -mtime +30 -delete

# Upload to cloud storage (optional)
aws s3 cp "$BACKUP_DIR/pitas_backup_$DATE.sql.gz" s3://your-backup-bucket/
```

### 2. Automated Backup with Cron

```bash
# Add to crontab
0 2 * * * /scripts/backup.sh
```

## 🚀 **Deployment Commands**

### Production Deployment

```bash
# 1. Deploy infrastructure
kubectl apply -f k8s/namespace.yaml
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secrets.yaml

# 2. Deploy database
kubectl apply -f k8s/postgres.yaml

# 3. Run migrations
kubectl exec -it postgres-pod -- psql -U pitas_user -d pitas_prod -c "SELECT version();"
kubectl run migration --image=pitas:latest --rm -it -- alembic upgrade head

# 4. Deploy application
kubectl apply -f k8s/deployment.yaml
kubectl apply -f k8s/service.yaml
kubectl apply -f k8s/ingress.yaml

# 5. Verify deployment
kubectl get pods -n pitas
kubectl get services -n pitas
kubectl logs -f deployment/pitas-api -n pitas
```

### Health Checks

```bash
# Check API health
curl https://pitas.yourdomain.com/health

# Check database connectivity
curl https://pitas.yourdomain.com/health/db

# Check Redis connectivity
curl https://pitas.yourdomain.com/health/redis

# Check integrations
curl https://pitas.yourdomain.com/api/v1/integrations/health
```

## 📈 **Performance Tuning**

### 1. Application Tuning

```python
# gunicorn.conf.py
bind = "0.0.0.0:8000"
workers = 4
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
```

### 2. Database Tuning

```sql
-- PostgreSQL performance tuning
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '512MB';
ALTER SYSTEM SET effective_cache_size = '2GB';
ALTER SYSTEM SET work_mem = '4MB';
ALTER SYSTEM SET maintenance_work_mem = '128MB';
```

## 🔧 **Troubleshooting**

### Common Issues

1. **Database Connection Issues**
   ```bash
   # Check database connectivity
   kubectl exec -it postgres-pod -- pg_isready
   
   # Check connection string
   kubectl logs deployment/pitas-api | grep "database"
   ```

2. **Redis Connection Issues**
   ```bash
   # Check Redis connectivity
   kubectl exec -it redis-pod -- redis-cli ping
   
   # Check Redis logs
   kubectl logs deployment/redis
   ```

3. **SSL Certificate Issues**
   ```bash
   # Check certificate validity
   openssl x509 -in /etc/ssl/certs/pitas.crt -text -noout
   
   # Renew Let's Encrypt certificate
   certbot renew --nginx
   ```

## 📞 **Support and Maintenance**

### Monitoring Alerts

Set up alerts for:
- API response time > 1 second
- Database connection failures
- High memory usage (>80%)
- SSL certificate expiration (30 days)
- Failed authentication attempts

### Regular Maintenance

- **Daily**: Check system health and logs
- **Weekly**: Review performance metrics and capacity
- **Monthly**: Update dependencies and security patches
- **Quarterly**: Review and update security configurations

---

## 🎉 **Deployment Complete!**

Your PITAS system is now ready for enterprise production use. The system provides:

✅ **High Availability** with load balancing and failover  
✅ **Enterprise Security** with SSL/TLS and SSO integration  
✅ **Comprehensive Monitoring** with metrics and alerting  
✅ **Automated Backups** with disaster recovery  
✅ **Performance Optimization** for enterprise scale  

**PITAS: Ready to revolutionize pentesting team management! 🚀**
