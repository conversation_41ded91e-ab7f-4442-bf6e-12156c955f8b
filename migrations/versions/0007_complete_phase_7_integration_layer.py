"""Complete Phase 7 - Integration Layer for Enterprise Systems

Revision ID: 0007_phase_7_complete
Revises: 0003_phase_3_complete
Create Date: 2025-06-17 19:30:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '0007_phase_7_complete'
down_revision: Union[str, None] = '0003_phase_3_complete'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema for Phase 7 Integration Layer."""
    
    # Add new integration types to existing enum (if using enum)
    # Note: This is a simplified approach. In production, you might need to handle enum updates differently
    
    # Add new columns to integrations table for Phase 7 enhancements
    op.add_column('integrations', sa.Column('priority', sa.Integer(), nullable=False, server_default='5'))
    op.add_column('integrations', sa.Column('tags', sa.J<PERSON>(), nullable=True))
    op.add_column('integrations', sa.Column('rate_limit_requests', sa.Integer(), nullable=False, server_default='100'))
    op.add_column('integrations', sa.Column('rate_limit_window', sa.Integer(), nullable=False, server_default='60'))
    op.add_column('integrations', sa.Column('health_check_interval', sa.Integer(), nullable=False, server_default='300'))
    op.add_column('integrations', sa.Column('auto_retry', sa.Boolean(), nullable=False, server_default='true'))
    op.add_column('integrations', sa.Column('notification_settings', sa.JSON(), nullable=True))
    
    # Create integration_api_keys table for API gateway
    op.create_table(
        'integration_api_keys',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('integration_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('key_name', sa.String(length=255), nullable=False),
        sa.Column('api_key_hash', sa.String(length=255), nullable=False),
        sa.Column('permissions', sa.JSON(), nullable=True),
        sa.Column('rate_limit', sa.JSON(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('last_used', sa.DateTime(timezone=True), nullable=True),
        sa.Column('usage_count', sa.Integer(), nullable=False, default=0),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(['integration_id'], ['integrations.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('api_key_hash')
    )
    
    # Create integration_webhooks table
    op.create_table(
        'integration_webhooks',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('integration_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('webhook_type', sa.String(length=50), nullable=False),
        sa.Column('endpoint_url', sa.String(length=500), nullable=False),
        sa.Column('secret_key', sa.String(length=255), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('retry_count', sa.Integer(), nullable=False, default=0),
        sa.Column('max_retries', sa.Integer(), nullable=False, default=3),
        sa.Column('last_triggered', sa.DateTime(timezone=True), nullable=True),
        sa.Column('last_success', sa.DateTime(timezone=True), nullable=True),
        sa.Column('last_error', sa.Text(), nullable=True),
        sa.Column('webhook_config', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(['integration_id'], ['integrations.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create integration_health_checks table
    op.create_table(
        'integration_health_checks',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('integration_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('check_time', sa.DateTime(timezone=True), nullable=False),
        sa.Column('status', sa.String(length=20), nullable=False),
        sa.Column('response_time_ms', sa.Integer(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('health_score', sa.Numeric(precision=3, scale=2), nullable=True),
        sa.Column('check_details', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(['integration_id'], ['integrations.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create sso_sessions table for SSO tracking
    op.create_table(
        'sso_sessions',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('provider_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('session_token', sa.String(length=500), nullable=False),
        sa.Column('sso_user_id', sa.String(length=255), nullable=False),
        sa.Column('provider_type', sa.String(length=50), nullable=False),
        sa.Column('login_time', sa.DateTime(timezone=True), nullable=False),
        sa.Column('last_activity', sa.DateTime(timezone=True), nullable=False),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('ip_address', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.Text(), nullable=True),
        sa.Column('session_metadata', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['provider_id'], ['integrations.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('session_token')
    )
    
    # Create api_request_logs table for API gateway logging
    op.create_table(
        'api_request_logs',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('integration_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('request_time', sa.DateTime(timezone=True), nullable=False),
        sa.Column('method', sa.String(length=10), nullable=False),
        sa.Column('path', sa.String(length=500), nullable=False),
        sa.Column('status_code', sa.Integer(), nullable=False),
        sa.Column('response_time_ms', sa.Integer(), nullable=True),
        sa.Column('client_ip', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.Text(), nullable=True),
        sa.Column('request_size', sa.Integer(), nullable=True),
        sa.Column('response_size', sa.Integer(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('request_metadata', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(['integration_id'], ['integrations.id'], ondelete='SET NULL'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Add indexes for performance optimization
    op.create_index('idx_integration_priority', 'integrations', ['priority'])
    op.create_index('idx_integration_health_check_interval', 'integrations', ['health_check_interval'])
    op.create_index('idx_integration_rate_limit', 'integrations', ['rate_limit_requests', 'rate_limit_window'])
    
    op.create_index('idx_api_keys_integration', 'integration_api_keys', ['integration_id'])
    op.create_index('idx_api_keys_active', 'integration_api_keys', ['is_active'])
    op.create_index('idx_api_keys_expires', 'integration_api_keys', ['expires_at'])
    
    op.create_index('idx_webhooks_integration', 'integration_webhooks', ['integration_id'])
    op.create_index('idx_webhooks_type', 'integration_webhooks', ['webhook_type'])
    op.create_index('idx_webhooks_active', 'integration_webhooks', ['is_active'])
    
    op.create_index('idx_health_checks_integration', 'integration_health_checks', ['integration_id'])
    op.create_index('idx_health_checks_time', 'integration_health_checks', ['check_time'])
    op.create_index('idx_health_checks_status', 'integration_health_checks', ['status'])
    
    op.create_index('idx_sso_sessions_user', 'sso_sessions', ['user_id'])
    op.create_index('idx_sso_sessions_provider', 'sso_sessions', ['provider_id'])
    op.create_index('idx_sso_sessions_active', 'sso_sessions', ['is_active'])
    op.create_index('idx_sso_sessions_expires', 'sso_sessions', ['expires_at'])
    
    op.create_index('idx_api_logs_integration', 'api_request_logs', ['integration_id'])
    op.create_index('idx_api_logs_time', 'api_request_logs', ['request_time'])
    op.create_index('idx_api_logs_status', 'api_request_logs', ['status_code'])
    op.create_index('idx_api_logs_path', 'api_request_logs', ['path'])
    
    # Add check constraints for data validation
    op.create_check_constraint(
        'ck_integration_priority_range',
        'integrations',
        'priority >= 1 AND priority <= 10'
    )
    
    op.create_check_constraint(
        'ck_integration_rate_limit_positive',
        'integrations',
        'rate_limit_requests >= 1 AND rate_limit_window >= 1'
    )
    
    op.create_check_constraint(
        'ck_integration_health_check_positive',
        'integrations',
        'health_check_interval >= 60'
    )
    
    op.create_check_constraint(
        'ck_api_keys_usage_positive',
        'integration_api_keys',
        'usage_count >= 0'
    )
    
    op.create_check_constraint(
        'ck_webhooks_retry_positive',
        'integration_webhooks',
        'retry_count >= 0 AND max_retries >= 0'
    )
    
    op.create_check_constraint(
        'ck_health_checks_response_time_positive',
        'integration_health_checks',
        'response_time_ms IS NULL OR response_time_ms >= 0'
    )
    
    op.create_check_constraint(
        'ck_health_checks_score_range',
        'integration_health_checks',
        'health_score IS NULL OR (health_score >= 0 AND health_score <= 10)'
    )
    
    op.create_check_constraint(
        'ck_api_logs_status_code_valid',
        'api_request_logs',
        'status_code >= 100 AND status_code <= 599'
    )
    
    op.create_check_constraint(
        'ck_api_logs_response_time_positive',
        'api_request_logs',
        'response_time_ms IS NULL OR response_time_ms >= 0'
    )
    
    op.create_check_constraint(
        'ck_api_logs_size_positive',
        'api_request_logs',
        '(request_size IS NULL OR request_size >= 0) AND (response_size IS NULL OR response_size >= 0)'
    )


def downgrade() -> None:
    """Downgrade schema for Phase 7."""
    # Drop check constraints
    op.drop_constraint('ck_api_logs_size_positive', 'api_request_logs')
    op.drop_constraint('ck_api_logs_response_time_positive', 'api_request_logs')
    op.drop_constraint('ck_api_logs_status_code_valid', 'api_request_logs')
    op.drop_constraint('ck_health_checks_score_range', 'integration_health_checks')
    op.drop_constraint('ck_health_checks_response_time_positive', 'integration_health_checks')
    op.drop_constraint('ck_webhooks_retry_positive', 'integration_webhooks')
    op.drop_constraint('ck_api_keys_usage_positive', 'integration_api_keys')
    op.drop_constraint('ck_integration_health_check_positive', 'integrations')
    op.drop_constraint('ck_integration_rate_limit_positive', 'integrations')
    op.drop_constraint('ck_integration_priority_range', 'integrations')
    
    # Drop indexes
    op.drop_index('idx_api_logs_path')
    op.drop_index('idx_api_logs_status')
    op.drop_index('idx_api_logs_time')
    op.drop_index('idx_api_logs_integration')
    op.drop_index('idx_sso_sessions_expires')
    op.drop_index('idx_sso_sessions_active')
    op.drop_index('idx_sso_sessions_provider')
    op.drop_index('idx_sso_sessions_user')
    op.drop_index('idx_health_checks_status')
    op.drop_index('idx_health_checks_time')
    op.drop_index('idx_health_checks_integration')
    op.drop_index('idx_webhooks_active')
    op.drop_index('idx_webhooks_type')
    op.drop_index('idx_webhooks_integration')
    op.drop_index('idx_api_keys_expires')
    op.drop_index('idx_api_keys_active')
    op.drop_index('idx_api_keys_integration')
    op.drop_index('idx_integration_rate_limit')
    op.drop_index('idx_integration_health_check_interval')
    op.drop_index('idx_integration_priority')
    
    # Drop tables
    op.drop_table('api_request_logs')
    op.drop_table('sso_sessions')
    op.drop_table('integration_health_checks')
    op.drop_table('integration_webhooks')
    op.drop_table('integration_api_keys')
    
    # Remove columns from integrations table
    op.drop_column('integrations', 'notification_settings')
    op.drop_column('integrations', 'auto_retry')
    op.drop_column('integrations', 'health_check_interval')
    op.drop_column('integrations', 'rate_limit_window')
    op.drop_column('integrations', 'rate_limit_requests')
    op.drop_column('integrations', 'tags')
    op.drop_column('integrations', 'priority')
