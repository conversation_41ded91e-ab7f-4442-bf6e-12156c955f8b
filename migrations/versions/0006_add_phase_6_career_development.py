"""Add Phase 6 employee retention and career development models

Revision ID: 0006_phase_6
Revises: 0008_phase_8
Create Date: 2025-06-17 16:45:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '0006_phase_6'
down_revision: Union[str, None] = '0008_phase_8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    
    # Create career_profiles table
    op.create_table(
        'career_profiles',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('employee_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('current_tier', sa.String(length=20), nullable=False),
        sa.Column('target_tier', sa.String(length=20), nullable=True),
        sa.Column('career_track', sa.String(length=30), nullable=False),
        sa.Column('years_experience', sa.Float(), nullable=False, default=0.0),
        sa.Column('career_goals', postgresql.JSONB(), nullable=True),
        sa.Column('long_term_aspirations', sa.Text(), nullable=True),
        sa.Column('preferred_work_style', postgresql.JSONB(), nullable=True),
        sa.Column('core_skills', postgresql.JSONB(), nullable=True),
        sa.Column('skill_gaps', postgresql.JSONB(), nullable=True),
        sa.Column('certifications', postgresql.JSONB(), nullable=True),
        sa.Column('last_promotion_date', sa.DateTime(timezone=True), nullable=True),
        sa.Column('next_review_date', sa.DateTime(timezone=True), nullable=True),
        sa.Column('promotion_readiness_score', sa.Float(), nullable=True),
        sa.Column('manager_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('mentor_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(['employee_id'], ['users.id']),
        sa.ForeignKeyConstraint(['manager_id'], ['users.id']),
        sa.ForeignKeyConstraint(['mentor_id'], ['users.id']),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('employee_id')
    )

    # Create employee_recognitions table
    op.create_table(
        'employee_recognitions',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('employee_profile_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('recognition_type', sa.String(length=30), nullable=False),
        sa.Column('recognition_title', sa.String(length=200), nullable=False),
        sa.Column('recognition_description', sa.Text(), nullable=False),
        sa.Column('nominated_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('approved_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('impact_description', sa.Text(), nullable=True),
        sa.Column('business_value', sa.Text(), nullable=True),
        sa.Column('recognition_value', sa.Float(), nullable=True),
        sa.Column('is_public', sa.Boolean(), nullable=False, default=True),
        sa.Column('shared_with_team', sa.Boolean(), nullable=False, default=True),
        sa.Column('shared_externally', sa.Boolean(), nullable=False, default=False),
        sa.Column('recognition_date', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(['employee_profile_id'], ['career_profiles.id']),
        sa.ForeignKeyConstraint(['nominated_by'], ['users.id']),
        sa.ForeignKeyConstraint(['approved_by'], ['users.id']),
        sa.PrimaryKeyConstraint('id')
    )

    # Create work_life_balance_metrics table
    op.create_table(
        'work_life_balance_metrics',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('employee_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('metric_date', sa.DateTime(timezone=True), nullable=False),
        sa.Column('week_start_date', sa.DateTime(timezone=True), nullable=False),
        sa.Column('hours_worked', sa.Float(), nullable=False),
        sa.Column('overtime_hours', sa.Float(), nullable=False, default=0.0),
        sa.Column('weekend_hours', sa.Float(), nullable=False, default=0.0),
        sa.Column('evening_hours', sa.Float(), nullable=False, default=0.0),
        sa.Column('work_life_balance_score', sa.Float(), nullable=True),
        sa.Column('stress_level', sa.Integer(), nullable=True),
        sa.Column('satisfaction_level', sa.Integer(), nullable=True),
        sa.Column('balance_status', sa.String(length=20), nullable=False, default='good'),
        sa.Column('burnout_risk_score', sa.Float(), nullable=False, default=0.0),
        sa.Column('alert_triggered', sa.Boolean(), nullable=False, default=False),
        sa.Column('remote_work_days', sa.Integer(), nullable=False, default=0),
        sa.Column('flexible_hours_used', sa.Boolean(), nullable=False, default=False),
        sa.Column('time_off_taken', sa.Float(), nullable=False, default=0.0),
        sa.Column('manager_notes', sa.Text(), nullable=True),
        sa.Column('intervention_taken', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(['employee_id'], ['users.id']),
        sa.PrimaryKeyConstraint('id')
    )

    # Add indexes for performance optimization
    op.create_index('idx_career_profiles_employee', 'career_profiles', ['employee_id'])
    op.create_index('idx_career_profiles_tier', 'career_profiles', ['current_tier'])
    op.create_index('idx_career_profiles_track', 'career_profiles', ['career_track'])
    op.create_index('idx_career_profiles_manager', 'career_profiles', ['manager_id'])
    op.create_index('idx_career_profiles_mentor', 'career_profiles', ['mentor_id'])
    op.create_index('idx_career_profiles_review_date', 'career_profiles', ['next_review_date'])
    op.create_index('idx_career_profiles_readiness', 'career_profiles', ['promotion_readiness_score'])
    
    op.create_index('idx_employee_recognitions_profile', 'employee_recognitions', ['employee_profile_id'])
    op.create_index('idx_employee_recognitions_type', 'employee_recognitions', ['recognition_type'])
    op.create_index('idx_employee_recognitions_date', 'employee_recognitions', ['recognition_date'])
    op.create_index('idx_employee_recognitions_nominated_by', 'employee_recognitions', ['nominated_by'])
    op.create_index('idx_employee_recognitions_approved_by', 'employee_recognitions', ['approved_by'])
    op.create_index('idx_employee_recognitions_public', 'employee_recognitions', ['is_public'])
    
    op.create_index('idx_work_life_balance_employee', 'work_life_balance_metrics', ['employee_id'])
    op.create_index('idx_work_life_balance_date', 'work_life_balance_metrics', ['metric_date'])
    op.create_index('idx_work_life_balance_week', 'work_life_balance_metrics', ['week_start_date'])
    op.create_index('idx_work_life_balance_status', 'work_life_balance_metrics', ['balance_status'])
    op.create_index('idx_work_life_balance_burnout', 'work_life_balance_metrics', ['burnout_risk_score'])
    op.create_index('idx_work_life_balance_alert', 'work_life_balance_metrics', ['alert_triggered'])
    op.create_index('idx_work_life_balance_employee_week', 'work_life_balance_metrics', ['employee_id', 'week_start_date'])

    # Add check constraints for data validation
    op.create_check_constraint(
        'ck_career_profiles_years_experience_positive',
        'career_profiles',
        'years_experience >= 0'
    )
    
    op.create_check_constraint(
        'ck_career_profiles_readiness_score_range',
        'career_profiles',
        'promotion_readiness_score IS NULL OR (promotion_readiness_score >= 0 AND promotion_readiness_score <= 100)'
    )
    
    op.create_check_constraint(
        'ck_employee_recognitions_value_positive',
        'employee_recognitions',
        'recognition_value IS NULL OR recognition_value >= 0'
    )
    
    op.create_check_constraint(
        'ck_work_life_balance_hours_positive',
        'work_life_balance_metrics',
        'hours_worked >= 0 AND overtime_hours >= 0 AND weekend_hours >= 0 AND evening_hours >= 0'
    )
    
    op.create_check_constraint(
        'ck_work_life_balance_score_range',
        'work_life_balance_metrics',
        'work_life_balance_score IS NULL OR (work_life_balance_score >= 0 AND work_life_balance_score <= 100)'
    )
    
    op.create_check_constraint(
        'ck_work_life_balance_stress_range',
        'work_life_balance_metrics',
        'stress_level IS NULL OR (stress_level >= 1 AND stress_level <= 10)'
    )
    
    op.create_check_constraint(
        'ck_work_life_balance_satisfaction_range',
        'work_life_balance_metrics',
        'satisfaction_level IS NULL OR (satisfaction_level >= 1 AND satisfaction_level <= 10)'
    )
    
    op.create_check_constraint(
        'ck_work_life_balance_burnout_range',
        'work_life_balance_metrics',
        'burnout_risk_score >= 0 AND burnout_risk_score <= 100'
    )
    
    op.create_check_constraint(
        'ck_work_life_balance_remote_days_range',
        'work_life_balance_metrics',
        'remote_work_days >= 0 AND remote_work_days <= 7'
    )
    
    op.create_check_constraint(
        'ck_work_life_balance_time_off_positive',
        'work_life_balance_metrics',
        'time_off_taken >= 0'
    )


def downgrade() -> None:
    """Downgrade schema."""
    # Drop check constraints
    op.drop_constraint('ck_work_life_balance_time_off_positive', 'work_life_balance_metrics')
    op.drop_constraint('ck_work_life_balance_remote_days_range', 'work_life_balance_metrics')
    op.drop_constraint('ck_work_life_balance_burnout_range', 'work_life_balance_metrics')
    op.drop_constraint('ck_work_life_balance_satisfaction_range', 'work_life_balance_metrics')
    op.drop_constraint('ck_work_life_balance_stress_range', 'work_life_balance_metrics')
    op.drop_constraint('ck_work_life_balance_score_range', 'work_life_balance_metrics')
    op.drop_constraint('ck_work_life_balance_hours_positive', 'work_life_balance_metrics')
    op.drop_constraint('ck_employee_recognitions_value_positive', 'employee_recognitions')
    op.drop_constraint('ck_career_profiles_readiness_score_range', 'career_profiles')
    op.drop_constraint('ck_career_profiles_years_experience_positive', 'career_profiles')
    
    # Drop indexes
    op.drop_index('idx_work_life_balance_employee_week')
    op.drop_index('idx_work_life_balance_alert')
    op.drop_index('idx_work_life_balance_burnout')
    op.drop_index('idx_work_life_balance_status')
    op.drop_index('idx_work_life_balance_week')
    op.drop_index('idx_work_life_balance_date')
    op.drop_index('idx_work_life_balance_employee')
    
    op.drop_index('idx_employee_recognitions_public')
    op.drop_index('idx_employee_recognitions_approved_by')
    op.drop_index('idx_employee_recognitions_nominated_by')
    op.drop_index('idx_employee_recognitions_date')
    op.drop_index('idx_employee_recognitions_type')
    op.drop_index('idx_employee_recognitions_profile')
    
    op.drop_index('idx_career_profiles_readiness')
    op.drop_index('idx_career_profiles_review_date')
    op.drop_index('idx_career_profiles_mentor')
    op.drop_index('idx_career_profiles_manager')
    op.drop_index('idx_career_profiles_track')
    op.drop_index('idx_career_profiles_tier')
    op.drop_index('idx_career_profiles_employee')
    
    # Drop tables
    op.drop_table('work_life_balance_metrics')
    op.drop_table('employee_recognitions')
    op.drop_table('career_profiles')
