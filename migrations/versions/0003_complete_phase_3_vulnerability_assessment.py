"""Complete Phase 3 vulnerability assessment and density tracking

Revision ID: 0003_phase_3_complete
Revises: 0006_phase_6
Create Date: 2025-06-17 18:30:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '0003_phase_3_complete'
down_revision: Union[str, None] = '0006_phase_6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    
    # Create vulnerability_density_metrics table
    op.create_table(
        'vulnerability_density_metrics',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('asset_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('metric_type', sa.String(length=50), nullable=False),
        sa.Column('calculation_date', sa.DateTime(timezone=True), nullable=False),
        sa.Column('vulnerability_count', sa.Integer(), nullable=False, default=0),
        sa.Column('asset_count', sa.Integer(), nullable=False, default=1),
        sa.Column('density_score', sa.Numeric(precision=10, scale=4), nullable=False),
        sa.Column('weighted_density_score', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('risk_adjusted_density', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('trend_direction', sa.String(length=20), nullable=True),
        sa.Column('period_comparison', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(['asset_id'], ['assets.id']),
        sa.PrimaryKeyConstraint('id')
    )

    # Create vulnerability_trend_analysis table
    op.create_table(
        'vulnerability_trend_analysis',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('analysis_type', sa.String(length=50), nullable=False),
        sa.Column('analysis_date', sa.DateTime(timezone=True), nullable=False),
        sa.Column('time_period_days', sa.Integer(), nullable=False),
        sa.Column('asset_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('current_value', sa.Numeric(precision=10, scale=4), nullable=False),
        sa.Column('previous_value', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('change_percentage', sa.Numeric(precision=6, scale=2), nullable=True),
        sa.Column('trend_direction', sa.String(length=20), nullable=False),
        sa.Column('trend_strength', sa.Numeric(precision=4, scale=2), nullable=True),
        sa.Column('forecast_next_period', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('forecast_confidence', sa.Numeric(precision=4, scale=2), nullable=True),
        sa.Column('seasonal_pattern', sa.JSON(), nullable=True),
        sa.Column('anomaly_detected', sa.Boolean(), nullable=False, default=False),
        sa.Column('anomaly_score', sa.Numeric(precision=4, scale=2), nullable=True),
        sa.Column('analysis_metadata', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(['asset_id'], ['assets.id']),
        sa.PrimaryKeyConstraint('id')
    )

    # Create vulnerability_risk_scores table
    op.create_table(
        'vulnerability_risk_scores',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('vulnerability_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('asset_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('calculated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('cvss_base_score', sa.Numeric(precision=3, scale=1), nullable=True),
        sa.Column('exploitability_score', sa.Numeric(precision=3, scale=1), nullable=True),
        sa.Column('impact_score', sa.Numeric(precision=3, scale=1), nullable=True),
        sa.Column('business_criticality_multiplier', sa.Numeric(precision=3, scale=2), nullable=False, default=1.0),
        sa.Column('asset_exposure_score', sa.Numeric(precision=3, scale=1), nullable=True),
        sa.Column('data_sensitivity_score', sa.Numeric(precision=3, scale=1), nullable=True),
        sa.Column('exploit_availability', sa.Boolean(), nullable=False, default=False),
        sa.Column('active_exploitation', sa.Boolean(), nullable=False, default=False),
        sa.Column('threat_actor_interest', sa.Numeric(precision=3, scale=1), nullable=True),
        sa.Column('compensating_controls_score', sa.Numeric(precision=3, scale=1), nullable=True),
        sa.Column('environmental_score', sa.Numeric(precision=3, scale=1), nullable=True),
        sa.Column('temporal_score', sa.Numeric(precision=3, scale=1), nullable=True),
        sa.Column('business_risk_score', sa.Numeric(precision=4, scale=1), nullable=False),
        sa.Column('total_risk_score', sa.Numeric(precision=4, scale=1), nullable=False),
        sa.Column('risk_category', sa.String(length=20), nullable=False),
        sa.Column('remediation_priority', sa.Integer(), nullable=False),
        sa.Column('risk_factors', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(['vulnerability_id'], ['vulnerabilities.id']),
        sa.ForeignKeyConstraint(['asset_id'], ['assets.id']),
        sa.PrimaryKeyConstraint('id')
    )

    # Create vulnerability_lifecycle table
    op.create_table(
        'vulnerability_lifecycle',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('vulnerability_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('current_status', sa.String(length=20), nullable=False),
        sa.Column('discovered_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('acknowledged_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('assigned_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('remediation_started_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('remediation_completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('verified_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('closed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('sla_category', sa.String(length=20), nullable=False),
        sa.Column('sla_target_hours', sa.Integer(), nullable=False),
        sa.Column('sla_due_date', sa.DateTime(timezone=True), nullable=False),
        sa.Column('sla_breach_date', sa.DateTime(timezone=True), nullable=True),
        sa.Column('sla_status', sa.String(length=20), nullable=False, default='on_track'),
        sa.Column('assigned_to', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('assigned_team', sa.String(length=100), nullable=True),
        sa.Column('escalation_level', sa.Integer(), nullable=False, default=0),
        sa.Column('remediation_progress', sa.Integer(), nullable=False, default=0),
        sa.Column('verification_attempts', sa.Integer(), nullable=False, default=0),
        sa.Column('reopen_count', sa.Integer(), nullable=False, default=0),
        sa.Column('time_to_acknowledge_hours', sa.Numeric(precision=8, scale=2), nullable=True),
        sa.Column('time_to_assign_hours', sa.Numeric(precision=8, scale=2), nullable=True),
        sa.Column('time_to_remediate_hours', sa.Numeric(precision=8, scale=2), nullable=True),
        sa.Column('time_to_verify_hours', sa.Numeric(precision=8, scale=2), nullable=True),
        sa.Column('total_lifecycle_hours', sa.Numeric(precision=8, scale=2), nullable=True),
        sa.Column('lifecycle_metadata', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(['vulnerability_id'], ['vulnerabilities.id']),
        sa.ForeignKeyConstraint(['assigned_to'], ['users.id']),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('vulnerability_id')
    )

    # Add indexes for performance optimization
    op.create_index('idx_density_asset', 'vulnerability_density_metrics', ['asset_id'])
    op.create_index('idx_density_calculation_date', 'vulnerability_density_metrics', ['calculation_date'])
    op.create_index('idx_density_metric_type', 'vulnerability_density_metrics', ['metric_type'])
    
    op.create_index('idx_trend_analysis_date', 'vulnerability_trend_analysis', ['analysis_date'])
    op.create_index('idx_trend_analysis_type', 'vulnerability_trend_analysis', ['analysis_type'])
    op.create_index('idx_trend_asset', 'vulnerability_trend_analysis', ['asset_id'])
    
    op.create_index('idx_risk_score_vuln', 'vulnerability_risk_scores', ['vulnerability_id'])
    op.create_index('idx_risk_score_asset', 'vulnerability_risk_scores', ['asset_id'])
    op.create_index('idx_risk_score_calculated', 'vulnerability_risk_scores', ['calculated_at'])
    op.create_index('idx_risk_score_total', 'vulnerability_risk_scores', ['total_risk_score'])
    
    op.create_index('idx_lifecycle_vuln', 'vulnerability_lifecycle', ['vulnerability_id'])
    op.create_index('idx_lifecycle_status', 'vulnerability_lifecycle', ['current_status'])
    op.create_index('idx_lifecycle_sla', 'vulnerability_lifecycle', ['sla_breach_date'])

    # Add check constraints for data validation
    op.create_check_constraint(
        'ck_density_vulnerability_count_positive',
        'vulnerability_density_metrics',
        'vulnerability_count >= 0'
    )
    
    op.create_check_constraint(
        'ck_density_asset_count_positive',
        'vulnerability_density_metrics',
        'asset_count >= 1'
    )
    
    op.create_check_constraint(
        'ck_density_score_positive',
        'vulnerability_density_metrics',
        'density_score >= 0'
    )
    
    op.create_check_constraint(
        'ck_trend_time_period_positive',
        'vulnerability_trend_analysis',
        'time_period_days >= 1'
    )
    
    op.create_check_constraint(
        'ck_trend_strength_range',
        'vulnerability_trend_analysis',
        'trend_strength IS NULL OR (trend_strength >= 0 AND trend_strength <= 1)'
    )
    
    op.create_check_constraint(
        'ck_forecast_confidence_range',
        'vulnerability_trend_analysis',
        'forecast_confidence IS NULL OR (forecast_confidence >= 0 AND forecast_confidence <= 1)'
    )
    
    op.create_check_constraint(
        'ck_risk_cvss_range',
        'vulnerability_risk_scores',
        'cvss_base_score IS NULL OR (cvss_base_score >= 0 AND cvss_base_score <= 10)'
    )
    
    op.create_check_constraint(
        'ck_risk_business_criticality_range',
        'vulnerability_risk_scores',
        'business_criticality_multiplier >= 0.5 AND business_criticality_multiplier <= 2.0'
    )
    
    op.create_check_constraint(
        'ck_risk_total_score_range',
        'vulnerability_risk_scores',
        'total_risk_score >= 0 AND total_risk_score <= 10'
    )
    
    op.create_check_constraint(
        'ck_risk_priority_positive',
        'vulnerability_risk_scores',
        'remediation_priority >= 1'
    )
    
    op.create_check_constraint(
        'ck_lifecycle_sla_target_positive',
        'vulnerability_lifecycle',
        'sla_target_hours >= 1'
    )
    
    op.create_check_constraint(
        'ck_lifecycle_escalation_positive',
        'vulnerability_lifecycle',
        'escalation_level >= 0'
    )
    
    op.create_check_constraint(
        'ck_lifecycle_progress_range',
        'vulnerability_lifecycle',
        'remediation_progress >= 0 AND remediation_progress <= 100'
    )
    
    op.create_check_constraint(
        'ck_lifecycle_verification_positive',
        'vulnerability_lifecycle',
        'verification_attempts >= 0'
    )
    
    op.create_check_constraint(
        'ck_lifecycle_reopen_positive',
        'vulnerability_lifecycle',
        'reopen_count >= 0'
    )


def downgrade() -> None:
    """Downgrade schema."""
    # Drop check constraints
    op.drop_constraint('ck_lifecycle_reopen_positive', 'vulnerability_lifecycle')
    op.drop_constraint('ck_lifecycle_verification_positive', 'vulnerability_lifecycle')
    op.drop_constraint('ck_lifecycle_progress_range', 'vulnerability_lifecycle')
    op.drop_constraint('ck_lifecycle_escalation_positive', 'vulnerability_lifecycle')
    op.drop_constraint('ck_lifecycle_sla_target_positive', 'vulnerability_lifecycle')
    op.drop_constraint('ck_risk_priority_positive', 'vulnerability_risk_scores')
    op.drop_constraint('ck_risk_total_score_range', 'vulnerability_risk_scores')
    op.drop_constraint('ck_risk_business_criticality_range', 'vulnerability_risk_scores')
    op.drop_constraint('ck_risk_cvss_range', 'vulnerability_risk_scores')
    op.drop_constraint('ck_forecast_confidence_range', 'vulnerability_trend_analysis')
    op.drop_constraint('ck_trend_strength_range', 'vulnerability_trend_analysis')
    op.drop_constraint('ck_trend_time_period_positive', 'vulnerability_trend_analysis')
    op.drop_constraint('ck_density_score_positive', 'vulnerability_density_metrics')
    op.drop_constraint('ck_density_asset_count_positive', 'vulnerability_density_metrics')
    op.drop_constraint('ck_density_vulnerability_count_positive', 'vulnerability_density_metrics')
    
    # Drop indexes
    op.drop_index('idx_lifecycle_sla')
    op.drop_index('idx_lifecycle_status')
    op.drop_index('idx_lifecycle_vuln')
    op.drop_index('idx_risk_score_total')
    op.drop_index('idx_risk_score_calculated')
    op.drop_index('idx_risk_score_asset')
    op.drop_index('idx_risk_score_vuln')
    op.drop_index('idx_trend_asset')
    op.drop_index('idx_trend_analysis_type')
    op.drop_index('idx_trend_analysis_date')
    op.drop_index('idx_density_metric_type')
    op.drop_index('idx_density_calculation_date')
    op.drop_index('idx_density_asset')
    
    # Drop tables
    op.drop_table('vulnerability_lifecycle')
    op.drop_table('vulnerability_risk_scores')
    op.drop_table('vulnerability_trend_analysis')
    op.drop_table('vulnerability_density_metrics')
