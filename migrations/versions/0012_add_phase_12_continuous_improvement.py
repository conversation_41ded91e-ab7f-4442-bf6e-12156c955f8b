"""Add Phase 12 continuous improvement and innovation models

Revision ID: 0012_phase_12
Revises: 83d1015e8f09
Create Date: 2025-06-16 20:45:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '0012_phase_12'
down_revision: Union[str, None] = '83d1015e8f09'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    
    # Create user_feedback table
    op.create_table(
        'user_feedback',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('feedback_type', sa.String(length=50), nullable=False),
        sa.Column('title', sa.String(length=200), nullable=False),
        sa.Column('description', sa.Text(), nullable=False),
        sa.Column('rating', sa.Integer(), nullable=True),
        sa.Column('nps_score', sa.Integer(), nullable=True),
        sa.Column('feature_area', sa.String(length=100), nullable=True),
        sa.Column('workflow_step', sa.String(length=100), nullable=True),
        sa.Column('page_url', sa.String(length=500), nullable=True),
        sa.Column('user_agent', sa.String(length=500), nullable=True),
        sa.Column('session_id', sa.String(length=100), nullable=True),
        sa.Column('sentiment_score', sa.String(length=20), nullable=True),
        sa.Column('priority_score', sa.Float(), nullable=True),
        sa.Column('category_tags', sa.JSON(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=False, default='submitted'),
        sa.Column('assigned_to', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('resolution_notes', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('resolved_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id']),
        sa.ForeignKeyConstraint(['assigned_to'], ['users.id']),
        sa.PrimaryKeyConstraint('id')
    )

    # Create feedback_responses table
    op.create_table(
        'feedback_responses',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('feedback_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('responder_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('response_text', sa.Text(), nullable=False),
        sa.Column('is_public', sa.Boolean(), nullable=False, default=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['feedback_id'], ['user_feedback.id']),
        sa.ForeignKeyConstraint(['responder_id'], ['users.id']),
        sa.PrimaryKeyConstraint('id')
    )

    # Create user_behavior_analytics table
    op.create_table(
        'user_behavior_analytics',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('session_id', sa.String(length=100), nullable=False),
        sa.Column('event_type', sa.String(length=100), nullable=False),
        sa.Column('event_data', sa.JSON(), nullable=False),
        sa.Column('page_url', sa.String(length=500), nullable=False),
        sa.Column('referrer_url', sa.String(length=500), nullable=True),
        sa.Column('page_load_time', sa.Float(), nullable=True),
        sa.Column('time_on_page', sa.Float(), nullable=True),
        sa.Column('click_count', sa.Integer(), nullable=True),
        sa.Column('scroll_depth', sa.Float(), nullable=True),
        sa.Column('user_agent', sa.String(length=500), nullable=False),
        sa.Column('screen_resolution', sa.String(length=20), nullable=True),
        sa.Column('viewport_size', sa.String(length=20), nullable=True),
        sa.Column('timestamp', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id']),
        sa.PrimaryKeyConstraint('id')
    )

    # Create feature_usage_metrics table
    op.create_table(
        'feature_usage_metrics',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('feature_name', sa.String(length=100), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('usage_count', sa.Integer(), nullable=False, default=1),
        sa.Column('total_time_spent', sa.Float(), nullable=False, default=0.0),
        sa.Column('success_rate', sa.Float(), nullable=True),
        sa.Column('error_count', sa.Integer(), nullable=False, default=0),
        sa.Column('usage_context', sa.JSON(), nullable=True),
        sa.Column('first_used', sa.DateTime(), nullable=False),
        sa.Column('last_used', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id']),
        sa.PrimaryKeyConstraint('id')
    )

    # Create satisfaction_surveys table
    op.create_table(
        'satisfaction_surveys',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('survey_type', sa.String(length=50), nullable=False),
        sa.Column('nps_score', sa.Integer(), nullable=True),
        sa.Column('satisfaction_rating', sa.Integer(), nullable=True),
        sa.Column('likelihood_to_recommend', sa.Integer(), nullable=True),
        sa.Column('what_works_well', sa.Text(), nullable=True),
        sa.Column('improvement_suggestions', sa.Text(), nullable=True),
        sa.Column('additional_comments', sa.Text(), nullable=True),
        sa.Column('survey_version', sa.String(length=20), nullable=False, default='1.0'),
        sa.Column('completion_time_seconds', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id']),
        sa.PrimaryKeyConstraint('id')
    )

    # Create innovation_projects table
    op.create_table(
        'innovation_projects',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('title', sa.String(length=200), nullable=False),
        sa.Column('description', sa.Text(), nullable=False),
        sa.Column('category', sa.String(length=50), nullable=False),
        sa.Column('priority', sa.String(length=20), nullable=False),
        sa.Column('status', sa.String(length=30), nullable=False, default='idea'),
        sa.Column('proposed_by', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('assigned_to', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('team_members', sa.JSON(), nullable=True),
        sa.Column('business_value', sa.Text(), nullable=False),
        sa.Column('expected_roi', sa.Float(), nullable=True),
        sa.Column('estimated_cost', sa.Float(), nullable=True),
        sa.Column('estimated_timeline_weeks', sa.Integer(), nullable=True),
        sa.Column('technical_requirements', sa.Text(), nullable=True),
        sa.Column('dependencies', sa.JSON(), nullable=True),
        sa.Column('risks', sa.Text(), nullable=True),
        sa.Column('success_criteria', sa.Text(), nullable=True),
        sa.Column('feasibility_score', sa.Float(), nullable=True),
        sa.Column('impact_score', sa.Float(), nullable=True),
        sa.Column('effort_score', sa.Float(), nullable=True),
        sa.Column('risk_score', sa.Float(), nullable=True),
        sa.Column('overall_score', sa.Float(), nullable=True),
        sa.Column('progress_percentage', sa.Float(), nullable=False, default=0.0),
        sa.Column('milestones_completed', sa.Integer(), nullable=False, default=0),
        sa.Column('total_milestones', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('started_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['proposed_by'], ['users.id']),
        sa.ForeignKeyConstraint(['assigned_to'], ['users.id']),
        sa.PrimaryKeyConstraint('id')
    )

    # Create innovation_evaluations table
    op.create_table(
        'innovation_evaluations',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('project_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('evaluator_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('technical_feasibility', sa.Float(), nullable=False),
        sa.Column('business_impact', sa.Float(), nullable=False),
        sa.Column('implementation_effort', sa.Float(), nullable=False),
        sa.Column('risk_assessment', sa.Float(), nullable=False),
        sa.Column('strategic_alignment', sa.Float(), nullable=False),
        sa.Column('strengths', sa.Text(), nullable=True),
        sa.Column('weaknesses', sa.Text(), nullable=True),
        sa.Column('recommendations', sa.Text(), nullable=True),
        sa.Column('additional_notes', sa.Text(), nullable=True),
        sa.Column('recommendation', sa.String(length=20), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['project_id'], ['innovation_projects.id']),
        sa.ForeignKeyConstraint(['evaluator_id'], ['users.id']),
        sa.PrimaryKeyConstraint('id')
    )

    # Create innovation_milestones table
    op.create_table(
        'innovation_milestones',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('project_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('title', sa.String(length=200), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('deliverables', sa.JSON(), nullable=True),
        sa.Column('planned_start_date', sa.DateTime(), nullable=True),
        sa.Column('planned_end_date', sa.DateTime(), nullable=True),
        sa.Column('actual_start_date', sa.DateTime(), nullable=True),
        sa.Column('actual_end_date', sa.DateTime(), nullable=True),
        sa.Column('is_completed', sa.Boolean(), nullable=False, default=False),
        sa.Column('completion_percentage', sa.Float(), nullable=False, default=0.0),
        sa.Column('depends_on', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['project_id'], ['innovation_projects.id']),
        sa.PrimaryKeyConstraint('id')
    )

    # Create technology_trends table
    op.create_table(
        'technology_trends',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('name', sa.String(length=200), nullable=False),
        sa.Column('description', sa.Text(), nullable=False),
        sa.Column('category', sa.String(length=50), nullable=False),
        sa.Column('maturity_level', sa.String(length=50), nullable=False),
        sa.Column('adoption_rate', sa.Float(), nullable=True),
        sa.Column('market_impact', sa.Float(), nullable=True),
        sa.Column('relevance_score', sa.Float(), nullable=True),
        sa.Column('source_urls', sa.JSON(), nullable=True),
        sa.Column('research_papers', sa.JSON(), nullable=True),
        sa.Column('vendor_information', sa.JSON(), nullable=True),
        sa.Column('first_identified', sa.DateTime(), nullable=False),
        sa.Column('last_updated', sa.DateTime(), nullable=False),
        sa.Column('opportunities', sa.Text(), nullable=True),
        sa.Column('threats', sa.Text(), nullable=True),
        sa.Column('implementation_considerations', sa.Text(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )

    # Create proof_of_concepts table
    op.create_table(
        'proof_of_concepts',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('innovation_project_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('title', sa.String(length=200), nullable=False),
        sa.Column('objective', sa.Text(), nullable=False),
        sa.Column('hypothesis', sa.Text(), nullable=False),
        sa.Column('methodology', sa.Text(), nullable=False),
        sa.Column('tools_used', sa.JSON(), nullable=True),
        sa.Column('resources_required', sa.JSON(), nullable=True),
        sa.Column('results_summary', sa.Text(), nullable=True),
        sa.Column('success_metrics', sa.JSON(), nullable=True),
        sa.Column('lessons_learned', sa.Text(), nullable=True),
        sa.Column('recommendations', sa.Text(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=False, default='planning'),
        sa.Column('is_successful', sa.Boolean(), nullable=True),
        sa.Column('start_date', sa.DateTime(), nullable=False),
        sa.Column('end_date', sa.DateTime(), nullable=True),
        sa.Column('duration_days', sa.Integer(), nullable=True),
        sa.Column('lead_researcher', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('team_members', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['innovation_project_id'], ['innovation_projects.id']),
        sa.ForeignKeyConstraint(['lead_researcher'], ['users.id']),
        sa.PrimaryKeyConstraint('id')
    )

    # Create ab_test_experiments table
    op.create_table(
        'ab_test_experiments',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('name', sa.String(length=200), nullable=False),
        sa.Column('description', sa.Text(), nullable=False),
        sa.Column('hypothesis', sa.Text(), nullable=False),
        sa.Column('experiment_type', sa.String(length=30), nullable=False),
        sa.Column('status', sa.String(length=20), nullable=False, default='draft'),
        sa.Column('target_audience', sa.JSON(), nullable=True),
        sa.Column('traffic_allocation', sa.Float(), nullable=False, default=100.0),
        sa.Column('control_variant', sa.JSON(), nullable=False),
        sa.Column('test_variants', sa.JSON(), nullable=False),
        sa.Column('primary_metric', sa.String(length=50), nullable=False),
        sa.Column('secondary_metrics', sa.JSON(), nullable=True),
        sa.Column('success_criteria', sa.Text(), nullable=False),
        sa.Column('confidence_level', sa.Float(), nullable=False, default=95.0),
        sa.Column('minimum_detectable_effect', sa.Float(), nullable=False, default=5.0),
        sa.Column('sample_size_required', sa.Integer(), nullable=True),
        sa.Column('start_date', sa.DateTime(), nullable=True),
        sa.Column('end_date', sa.DateTime(), nullable=True),
        sa.Column('planned_duration_days', sa.Integer(), nullable=True),
        sa.Column('is_statistically_significant', sa.Boolean(), nullable=True),
        sa.Column('winning_variant', sa.String(length=100), nullable=True),
        sa.Column('results_summary', sa.Text(), nullable=True),
        sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['created_by'], ['users.id']),
        sa.PrimaryKeyConstraint('id')
    )

    # Create experiment_participants table
    op.create_table(
        'experiment_participants',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('experiment_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('variant_assigned', sa.String(length=100), nullable=False),
        sa.Column('assignment_timestamp', sa.DateTime(), nullable=False),
        sa.Column('first_exposure', sa.DateTime(), nullable=True),
        sa.Column('last_exposure', sa.DateTime(), nullable=True),
        sa.Column('total_exposures', sa.Integer(), nullable=False, default=0),
        sa.Column('has_converted', sa.Boolean(), nullable=False, default=False),
        sa.Column('conversion_timestamp', sa.DateTime(), nullable=True),
        sa.Column('conversion_value', sa.Float(), nullable=True),
        sa.ForeignKeyConstraint(['experiment_id'], ['ab_test_experiments.id']),
        sa.ForeignKeyConstraint(['user_id'], ['users.id']),
        sa.PrimaryKeyConstraint('id')
    )

    # Create experiment_metrics table
    op.create_table(
        'experiment_metrics',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('experiment_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('participant_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('metric_type', sa.String(length=50), nullable=False),
        sa.Column('metric_value', sa.Float(), nullable=False),
        sa.Column('variant', sa.String(length=100), nullable=False),
        sa.Column('session_id', sa.String(length=100), nullable=True),
        sa.Column('page_url', sa.String(length=500), nullable=True),
        sa.Column('additional_data', sa.JSON(), nullable=True),
        sa.Column('recorded_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['experiment_id'], ['ab_test_experiments.id']),
        sa.ForeignKeyConstraint(['participant_id'], ['experiment_participants.id']),
        sa.PrimaryKeyConstraint('id')
    )

    # Create performance_benchmarks table
    op.create_table(
        'performance_benchmarks',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('metric_name', sa.String(length=100), nullable=False),
        sa.Column('metric_category', sa.String(length=50), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('current_value', sa.Float(), nullable=False),
        sa.Column('target_value', sa.Float(), nullable=True),
        sa.Column('baseline_value', sa.Float(), nullable=True),
        sa.Column('unit_of_measurement', sa.String(length=50), nullable=False),
        sa.Column('warning_threshold', sa.Float(), nullable=True),
        sa.Column('critical_threshold', sa.Float(), nullable=True),
        sa.Column('measurement_context', sa.JSON(), nullable=True),
        sa.Column('tags', sa.JSON(), nullable=True),
        sa.Column('measured_at', sa.DateTime(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )

    # Create performance_trends table
    op.create_table(
        'performance_trends',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('benchmark_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('value', sa.Float(), nullable=False),
        sa.Column('timestamp', sa.DateTime(), nullable=False),
        sa.Column('trend_direction', sa.String(length=20), nullable=True),
        sa.Column('percentage_change', sa.Float(), nullable=True),
        sa.Column('context_data', sa.JSON(), nullable=True),
        sa.ForeignKeyConstraint(['benchmark_id'], ['performance_benchmarks.id']),
        sa.PrimaryKeyConstraint('id')
    )

    # Create improvement_recommendations table
    op.create_table(
        'improvement_recommendations',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('title', sa.String(length=200), nullable=False),
        sa.Column('description', sa.Text(), nullable=False),
        sa.Column('category', sa.String(length=50), nullable=False),
        sa.Column('priority_score', sa.Float(), nullable=False),
        sa.Column('impact_score', sa.Float(), nullable=False),
        sa.Column('effort_score', sa.Float(), nullable=False),
        sa.Column('confidence_score', sa.Float(), nullable=False),
        sa.Column('suggested_actions', sa.JSON(), nullable=False),
        sa.Column('estimated_timeline', sa.String(length=100), nullable=True),
        sa.Column('required_resources', sa.JSON(), nullable=True),
        sa.Column('data_sources', sa.JSON(), nullable=False),
        sa.Column('supporting_evidence', sa.JSON(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=False, default='pending'),
        sa.Column('assigned_to', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('user_feedback', sa.Text(), nullable=True),
        sa.Column('implementation_notes', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['assigned_to'], ['users.id']),
        sa.PrimaryKeyConstraint('id')
    )


def downgrade() -> None:
    """Downgrade schema."""
    # Drop tables in reverse order
    op.drop_table('improvement_recommendations')
    op.drop_table('performance_trends')
    op.drop_table('performance_benchmarks')
    op.drop_table('experiment_metrics')
    op.drop_table('experiment_participants')
    op.drop_table('ab_test_experiments')
    op.drop_table('proof_of_concepts')
    op.drop_table('technology_trends')
    op.drop_table('innovation_milestones')
    op.drop_table('innovation_evaluations')
    op.drop_table('innovation_projects')
    op.drop_table('satisfaction_surveys')
    op.drop_table('feature_usage_metrics')
    op.drop_table('user_behavior_analytics')
    op.drop_table('feedback_responses')
    op.drop_table('user_feedback')
